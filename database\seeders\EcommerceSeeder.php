<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Product;
use App\Models\ProductVariation;
use App\Models\ShippingMethod;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class EcommerceSeeder extends Seeder
{
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'phone' => '081234567890',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create test user
        User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '081234567891',
            'password' => Hash::make('password'),
            'role' => 'user',
            'email_verified_at' => now(),
        ]);

        // Create categories
        $categories = [
            [
                'name' => 'Football',
                'slug' => 'football',
                'description' => 'Football equipment and gear',
                'children' => [
                    ['name' => 'Football Boots', 'slug' => 'football-boots'],
                    ['name' => 'Football Jerseys', 'slug' => 'football-jerseys'],
                    ['name' => 'Football Balls', 'slug' => 'football-balls'],
                ]
            ],
            [
                'name' => 'Basketball',
                'slug' => 'basketball',
                'description' => 'Basketball equipment and gear',
                'children' => [
                    ['name' => 'Basketball Shoes', 'slug' => 'basketball-shoes'],
                    ['name' => 'Basketball Jerseys', 'slug' => 'basketball-jerseys'],
                    ['name' => 'Basketball Balls', 'slug' => 'basketball-balls'],
                ]
            ],
            [
                'name' => 'Running',
                'slug' => 'running',
                'description' => 'Running equipment and gear',
                'children' => [
                    ['name' => 'Running Shoes', 'slug' => 'running-shoes'],
                    ['name' => 'Running Apparel', 'slug' => 'running-apparel'],
                    ['name' => 'Running Accessories', 'slug' => 'running-accessories'],
                ]
            ],
            [
                'name' => 'Fitness',
                'slug' => 'fitness',
                'description' => 'Fitness equipment and gear',
                'children' => [
                    ['name' => 'Gym Equipment', 'slug' => 'gym-equipment'],
                    ['name' => 'Fitness Apparel', 'slug' => 'fitness-apparel'],
                    ['name' => 'Supplements', 'slug' => 'supplements'],
                ]
            ],
        ];

        foreach ($categories as $categoryData) {
            $category = Category::create([
                'name' => $categoryData['name'],
                'slug' => $categoryData['slug'],
                'description' => $categoryData['description'],
                'is_active' => true,
                'sort_order' => 0,
            ]);

            foreach ($categoryData['children'] as $childData) {
                Category::create([
                    'name' => $childData['name'],
                    'slug' => $childData['slug'],
                    'parent_id' => $category->id,
                    'is_active' => true,
                    'sort_order' => 0,
                ]);
            }
        }

        // Create products
        $products = [
            [
                'name' => 'Nike Air Zoom Mercurial Superfly',
                'category' => 'football-boots',
                'brand' => 'Nike',
                'price' => 2500000,
                'sale_price' => 2000000,
                'description' => 'Professional football boots with advanced traction technology for superior performance on the field.',
                'short_description' => 'Professional football boots with advanced traction technology.',
                'weight' => 0.3,
                'is_featured' => true,
                'variations' => [
                    ['attributes' => ['size' => '40', 'color' => 'Black'], 'stock' => 10],
                    ['attributes' => ['size' => '41', 'color' => 'Black'], 'stock' => 15],
                    ['attributes' => ['size' => '42', 'color' => 'Black'], 'stock' => 12],
                    ['attributes' => ['size' => '40', 'color' => 'White'], 'stock' => 8],
                    ['attributes' => ['size' => '41', 'color' => 'White'], 'stock' => 10],
                    ['attributes' => ['size' => '42', 'color' => 'White'], 'stock' => 7],
                ]
            ],
            [
                'name' => 'Adidas Predator Edge',
                'category' => 'football-boots',
                'brand' => 'Adidas',
                'price' => 2200000,
                'description' => 'Precision and control football boots designed for the modern game.',
                'short_description' => 'Precision and control football boots.',
                'weight' => 0.32,
                'variations' => [
                    ['attributes' => ['size' => '40', 'color' => 'Red'], 'stock' => 12],
                    ['attributes' => ['size' => '41', 'color' => 'Red'], 'stock' => 18],
                    ['attributes' => ['size' => '42', 'color' => 'Red'], 'stock' => 15],
                ]
            ],
            [
                'name' => 'Nike Air Jordan 1 Retro High',
                'category' => 'basketball-shoes',
                'brand' => 'Nike',
                'price' => 3000000,
                'description' => 'Iconic basketball shoes that started it all. Classic design meets modern comfort.',
                'short_description' => 'Iconic basketball shoes with classic design.',
                'weight' => 0.6,
                'is_featured' => true,
                'variations' => [
                    ['attributes' => ['size' => '40', 'color' => 'Chicago'], 'stock' => 5],
                    ['attributes' => ['size' => '41', 'color' => 'Chicago'], 'stock' => 8],
                    ['attributes' => ['size' => '42', 'color' => 'Chicago'], 'stock' => 6],
                    ['attributes' => ['size' => '43', 'color' => 'Chicago'], 'stock' => 4],
                ]
            ],
            [
                'name' => 'Adidas Ultraboost 22',
                'category' => 'running-shoes',
                'brand' => 'Adidas',
                'price' => 2800000,
                'sale_price' => 2400000,
                'description' => 'Revolutionary running shoes with responsive cushioning and energy return.',
                'short_description' => 'Revolutionary running shoes with responsive cushioning.',
                'weight' => 0.31,
                'variations' => [
                    ['attributes' => ['size' => '40', 'color' => 'Core Black'], 'stock' => 20],
                    ['attributes' => ['size' => '41', 'color' => 'Core Black'], 'stock' => 25],
                    ['attributes' => ['size' => '42', 'color' => 'Core Black'], 'stock' => 22],
                    ['attributes' => ['size' => '40', 'color' => 'Cloud White'], 'stock' => 15],
                    ['attributes' => ['size' => '41', 'color' => 'Cloud White'], 'stock' => 18],
                ]
            ],
            [
                'name' => 'Wilson Official NBA Basketball',
                'category' => 'basketball-balls',
                'brand' => 'Wilson',
                'price' => 850000,
                'description' => 'Official NBA game basketball with premium leather construction.',
                'short_description' => 'Official NBA game basketball.',
                'weight' => 0.62,
                'stock_quantity' => 50,
            ],
            [
                'name' => 'Nike Dri-FIT Running Shirt',
                'category' => 'running-apparel',
                'brand' => 'Nike',
                'price' => 450000,
                'description' => 'Moisture-wicking running shirt for optimal comfort during workouts.',
                'short_description' => 'Moisture-wicking running shirt.',
                'weight' => 0.15,
                'variations' => [
                    ['attributes' => ['size' => 'S', 'color' => 'Black'], 'stock' => 30],
                    ['attributes' => ['size' => 'M', 'color' => 'Black'], 'stock' => 40],
                    ['attributes' => ['size' => 'L', 'color' => 'Black'], 'stock' => 35],
                    ['attributes' => ['size' => 'XL', 'color' => 'Black'], 'stock' => 25],
                    ['attributes' => ['size' => 'S', 'color' => 'Navy'], 'stock' => 25],
                    ['attributes' => ['size' => 'M', 'color' => 'Navy'], 'stock' => 35],
                    ['attributes' => ['size' => 'L', 'color' => 'Navy'], 'stock' => 30],
                ]
            ],
        ];

        foreach ($products as $productData) {
            $category = Category::where('slug', $productData['category'])->first();
            
            $product = Product::create([
                'name' => $productData['name'],
                'slug' => Str::slug($productData['name']),
                'description' => $productData['description'],
                'short_description' => $productData['short_description'],
                'sku' => 'SKU-' . strtoupper(Str::random(8)),
                'price' => $productData['price'],
                'sale_price' => $productData['sale_price'] ?? null,
                'stock_quantity' => $productData['stock_quantity'] ?? 0,
                'manage_stock' => isset($productData['variations']),
                'weight' => $productData['weight'],
                'category_id' => $category->id,
                'brand' => $productData['brand'],
                'is_featured' => $productData['is_featured'] ?? false,
                'is_active' => true,
                'rating' => rand(35, 50) / 10, // Random rating between 3.5 and 5.0
                'review_count' => rand(5, 100),
            ]);

            // Create variations if specified
            if (isset($productData['variations'])) {
                foreach ($productData['variations'] as $variationData) {
                    ProductVariation::create([
                        'product_id' => $product->id,
                        'sku' => $product->sku . '-' . strtoupper(Str::random(4)),
                        'price' => $productData['sale_price'] ?? $productData['price'],
                        'stock_quantity' => $variationData['stock'],
                        'attributes' => $variationData['attributes'],
                        'is_active' => true,
                    ]);
                }
            }
        }

        // Create shipping methods
        $shippingMethods = [
            [
                'name' => 'Regular Shipping',
                'code' => 'regular',
                'description' => 'Standard delivery service',
                'base_cost' => 15000,
                'cost_per_kg' => 5000,
                'estimated_days_min' => 3,
                'estimated_days_max' => 7,
                'free_shipping_threshold' => 500000,
            ],
            [
                'name' => 'Express Shipping',
                'code' => 'express',
                'description' => 'Fast delivery service',
                'base_cost' => 25000,
                'cost_per_kg' => 8000,
                'estimated_days_min' => 1,
                'estimated_days_max' => 3,
                'free_shipping_threshold' => 1000000,
            ],
            [
                'name' => 'Same Day Delivery',
                'code' => 'same_day',
                'description' => 'Same day delivery (Jakarta area only)',
                'base_cost' => 50000,
                'cost_per_kg' => 10000,
                'estimated_days_min' => 0,
                'estimated_days_max' => 1,
                'free_shipping_threshold' => 2000000,
            ],
        ];

        foreach ($shippingMethods as $methodData) {
            ShippingMethod::create($methodData);
        }
    }
}
