<?php

namespace App\Livewire\Components;

use App\Models\Product;
use App\Models\Wishlist;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class WishlistToggle extends Component
{
    public Product $product;
    public $isInWishlist = false;
    public $isLoading = false;

    public function mount(Product $product)
    {
        $this->product = $product;
        $this->checkWishlistStatus();
    }

    public function checkWishlistStatus()
    {
        if (Auth::check()) {
            $this->isInWishlist = Wishlist::where('user_id', Auth::id())
                ->where('product_id', $this->product->id)
                ->exists();
        }
    }

    public function toggleWishlist()
    {
        if (!Auth::check()) {
            $this->dispatch('notify', 'warning', '<PERSON>lakan login terlebih dahulu untuk menambahkan ke wishlist.');
            return;
        }

        $this->isLoading = true;

        try {
            $wishlistItem = Wishlist::where('user_id', Auth::id())
                ->where('product_id', $this->product->id)
                ->first();

            if ($wishlistItem) {
                // Remove from wishlist
                $wishlistItem->delete();
                $this->isInWishlist = false;
                $this->dispatch('notify', 'success', 'Produk dihapus dari wishlist!');
            } else {
                // Add to wishlist
                Wishlist::create([
                    'user_id' => Auth::id(),
                    'product_id' => $this->product->id,
                ]);
                $this->isInWishlist = true;
                $this->dispatch('notify', 'success', 'Produk ditambahkan ke wishlist!');
            }

            // Update wishlist counter if exists
            $this->dispatch('wishlist-updated');

        } catch (\Exception $e) {
            $this->dispatch('notify', 'error', 'Terjadi kesalahan saat memperbarui wishlist.');
        } finally {
            $this->isLoading = false;
        }
    }

    public function render()
    {
        return view('livewire.components.wishlist-toggle');
    }
}
