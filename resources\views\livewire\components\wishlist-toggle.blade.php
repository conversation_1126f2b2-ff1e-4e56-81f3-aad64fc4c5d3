<button 
    wire:click="toggleWishlist"
    wire:loading.attr="disabled"
    wire:target="toggleWishlist"
    class="group p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:scale-110 disabled:hover:scale-100"
    title="{{ $isInWishlist ? 'Hapus dari Wishlist' : 'Tambah ke Wishlist' }}"
>
    <div wire:loading.remove wire:target="toggleWishlist">
        @if($isInWishlist)
            <flux:icon name="heart" class="h-5 w-5 text-red-500 fill-current group-hover:scale-110 transition-transform duration-200" />
        @else
            <flux:icon name="heart" class="h-5 w-5 text-gray-400 group-hover:text-red-500 group-hover:scale-110 transition-all duration-200" />
        @endif
    </div>
    
    <div wire:loading wire:target="toggleWishlist">
        <div class="animate-spin rounded-full h-5 w-5 border-2 border-gray-300 border-t-red-500"></div>
    </div>
</button>
