<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(isset($title) ? $title . ' - ' : ''); ?><?php echo e(config('app.name', 'SportZone')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    @fluxStyles
</head>
<body class="font-sans antialiased h-full bg-gray-50 dark:bg-gray-900">
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo and Main Navigation -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <a href="<?php echo e(route('products.index')); ?>" wire:navigate class="text-xl font-bold text-gray-900 dark:text-white">
                            SportZone
                        </a>
                    </div>
                    <div class="hidden md:ml-8 md:flex md:space-x-8">
                        <a href="<?php echo e(route('products.index')); ?>" wire:navigate 
                           class="text-gray-900 dark:text-white hover:text-blue-600 px-3 py-2 text-sm font-medium">
                            Products
                        </a>
                        <a href="<?php echo e(route('products.index', ['category' => 'football'])); ?>" wire:navigate 
                           class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-3 py-2 text-sm font-medium">
                            Football
                        </a>
                        <a href="<?php echo e(route('products.index', ['category' => 'basketball'])); ?>" wire:navigate 
                           class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-3 py-2 text-sm font-medium">
                            Basketball
                        </a>
                        <a href="<?php echo e(route('products.index', ['category' => 'running'])); ?>" wire:navigate 
                           class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-3 py-2 text-sm font-medium">
                            Running
                        </a>
                        <a href="<?php echo e(route('products.index', ['category' => 'fitness'])); ?>" wire:navigate 
                           class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-3 py-2 text-sm font-medium">
                            Fitness
                        </a>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="flex-1 max-w-lg mx-8 hidden lg:block">
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('components.search-bar', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1227670891-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                </div>

                <!-- Right Navigation -->
                <div class="flex items-center space-x-4">
                    <!-- Cart -->
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('components.cart-counter', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1227670891-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

                    <?php if(auth()->guard()->check()): ?>
                        <!-- Wishlist -->
                        <a href="<?php echo e(route('wishlist')); ?>" wire:navigate class="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                            <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['name' => 'heart','class' => 'h-6 w-6']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'heart','class' => 'h-6 w-6']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                        </a>

                        <!-- User Menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <div class="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        <?php echo e(substr(auth()->user()->name, 0, 1)); ?>

                                    </span>
                                </div>
                            </button>

                            <div x-show="open" @click.away="open = false" 
                                 class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50">
                                <a href="<?php echo e(route('dashboard')); ?>" wire:navigate 
                                   class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    Dashboard
                                </a>
                                <a href="<?php echo e(route('orders.index')); ?>" wire:navigate 
                                   class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    My Orders
                                </a>
                                <a href="<?php echo e(route('wishlist')); ?>" wire:navigate 
                                   class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    Wishlist
                                </a>
                                <a href="<?php echo e(route('account.addresses')); ?>" wire:navigate
                                   class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    Address Book
                                </a>
                                <a href="<?php echo e(route('settings.profile')); ?>" wire:navigate 
                                   class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    Settings
                                </a>
                                <div class="border-t border-gray-100 dark:border-gray-700"></div>
                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('actions.logout', ['class' => 'block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700']);

$__html = app('livewire')->mount($__name, $__params, 'lw-1227670891-2', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            </div>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" wire:navigate 
                           class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-3 py-2 text-sm font-medium">
                            Login
                        </a>
                        <a href="<?php echo e(route('register')); ?>" wire:navigate 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Sign Up
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="md:hidden" x-data="{ open: false }">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="<?php echo e(route('products.index')); ?>" wire:navigate 
                   class="text-gray-900 dark:text-white block px-3 py-2 text-base font-medium">
                    Products
                </a>
                <a href="<?php echo e(route('products.index', ['category' => 'football'])); ?>" wire:navigate 
                   class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white block px-3 py-2 text-base font-medium">
                    Football
                </a>
                <a href="<?php echo e(route('products.index', ['category' => 'basketball'])); ?>" wire:navigate 
                   class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white block px-3 py-2 text-base font-medium">
                    Basketball
                </a>
                <a href="<?php echo e(route('products.index', ['category' => 'running'])); ?>" wire:navigate 
                   class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white block px-3 py-2 text-base font-medium">
                    Running
                </a>
                <a href="<?php echo e(route('products.index', ['category' => 'fitness'])); ?>" wire:navigate 
                   class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white block px-3 py-2 text-base font-medium">
                    Fitness
                </a>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <?php if(session()->has('success')): ?>
        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
            <span class="block sm:inline"><?php echo e(session('success')); ?></span>
        </div>
    <?php endif; ?>

    <?php if(session()->has('error')): ?>
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
            <span class="block sm:inline"><?php echo e(session('error')); ?></span>
        </div>
    <?php endif; ?>

    <?php if(session()->has('info')): ?>
        <div class="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
            <span class="block sm:inline"><?php echo e(session('info')); ?></span>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="min-h-screen">
        <?php echo e($slot); ?>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">SportZone</h3>
                    <p class="text-gray-300">Your one-stop shop for all sports equipment and gear.</p>
                </div>
                <div>
                    <h4 class="text-md font-semibold mb-4">Categories</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="<?php echo e(route('products.index', ['category' => 'football'])); ?>" wire:navigate class="hover:text-white">Football</a></li>
                        <li><a href="<?php echo e(route('products.index', ['category' => 'basketball'])); ?>" wire:navigate class="hover:text-white">Basketball</a></li>
                        <li><a href="<?php echo e(route('products.index', ['category' => 'running'])); ?>" wire:navigate class="hover:text-white">Running</a></li>
                        <li><a href="<?php echo e(route('products.index', ['category' => 'fitness'])); ?>" wire:navigate class="hover:text-white">Fitness</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-md font-semibold mb-4">Customer Service</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#" class="hover:text-white">Contact Us</a></li>
                        <li><a href="#" class="hover:text-white">Shipping Info</a></li>
                        <li><a href="#" class="hover:text-white">Returns</a></li>
                        <li><a href="#" class="hover:text-white">Size Guide</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-md font-semibold mb-4">Connect</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#" class="hover:text-white">Facebook</a></li>
                        <li><a href="#" class="hover:text-white">Instagram</a></li>
                        <li><a href="#" class="hover:text-white">Twitter</a></li>
                        <li><a href="#" class="hover:text-white">YouTube</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
                <p>&copy; <?php echo e(date('Y')); ?> SportZone. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <?php app('livewire')->forceAssetInjection(); ?>
<?php echo app('flux')->scripts(); ?>

    <script src="//unpkg.com/alpinejs" defer></script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Herd\projectuas6\resources\views/layouts/app.blade.php ENDPATH**/ ?>