<?php

namespace App\Livewire;

use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Component;

class Dashboard extends Component
{
    public function mount()
    {
        // Redirect admin users to admin dashboard
        if (Auth::check() && Auth::user()->isAdmin()) {
            return $this->redirect(route('admin.dashboard'));
        }
    }

    public function getRecentOrdersProperty()
    {
        return Auth::user()->orders()->latest()->take(3)->get();
    }

    #[Layout('layouts.app')]
    #[Title('Dashboard')]
    public function render()
    {
        return view('livewire.dashboard');
    }
}
