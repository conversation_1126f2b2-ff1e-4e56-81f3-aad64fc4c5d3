<div class="container mx-auto px-4 py-8">
    <!-- Welcome Header -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-2xl p-8 mb-8 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-4xl font-bold mb-2">Selamat datang kembali, {{ auth()->user()->name }}! 👋</h1>
                <p class="text-blue-100 text-lg">Siap menjelajahi peralatan olahraga terbaru kami?</p>
            </div>
            <div class="hidden md:block">
                <div class="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
                    <flux:icon name="user" class="h-12 w-12 text-white" />
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Quick Links -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                <flux:icon name="bolt" class="h-6 w-6 mr-2 text-blue-600" />
                Aksi Cepat
            </h2>
            <div class="grid grid-cols-2 gap-3">
                <a href="{{ route('products.index') }}" wire:navigate
                   class="group flex flex-col items-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-800/30 dark:hover:to-blue-700/30 transition-all duration-200">
                    <flux:icon name="shopping-bag" class="h-8 w-8 text-blue-600 mb-2 group-hover:scale-110 transition-transform duration-200" />
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Jelajahi Produk</span>
                </a>
                <a href="{{ route('orders.index') }}" wire:navigate
                   class="group flex flex-col items-center p-4 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg hover:from-green-100 hover:to-green-200 dark:hover:from-green-800/30 dark:hover:to-green-700/30 transition-all duration-200">
                    <flux:icon name="clipboard-document-list" class="h-8 w-8 text-green-600 mb-2 group-hover:scale-110 transition-transform duration-200" />
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Pesanan Saya</span>
                </a>
                <a href="{{ route('wishlist') }}" wire:navigate
                   class="group flex flex-col items-center p-4 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg hover:from-red-100 hover:to-red-200 dark:hover:from-red-800/30 dark:hover:to-red-700/30 transition-all duration-200">
                    <flux:icon name="heart" class="h-8 w-8 text-red-600 mb-2 group-hover:scale-110 transition-transform duration-200" />
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Wishlist Saya</span>
                </a>
                <a href="{{ route('account.addresses') }}" wire:navigate
                   class="group flex flex-col items-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg hover:from-purple-100 hover:to-purple-200 dark:hover:from-purple-800/30 dark:hover:to-purple-700/30 transition-all duration-200">
                    <flux:icon name="map-pin" class="h-8 w-8 text-purple-600 mb-2 group-hover:scale-110 transition-transform duration-200" />
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Buku Alamat</span>
                </a>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Pesanan Terbaru</h2>
            @if($this->recentOrders->count() > 0)
                <div class="space-y-3">
                    @foreach($this->recentOrders as $order)
                        <div class="border-b border-gray-200 dark:border-gray-700 pb-2 last:border-b-0">
                            <a href="{{ route('orders.show', $order) }}" wire:navigate
                               class="text-sm font-medium text-blue-600 hover:text-blue-800">
                                #{{ $order->order_number }}
                            </a>
                            <p class="text-xs text-gray-600 dark:text-gray-400">
                                {{ $order->created_at->format('d M Y') }} -
                                Rp {{ number_format($order->total_amount, 0, ',', '.') }}
                            </p>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-gray-600 dark:text-gray-400 text-sm">Belum ada pesanan</p>
            @endif
        </div>

        <!-- Account Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Account</h2>
            <div class="space-y-2 text-sm">
                <p><span class="text-gray-600 dark:text-gray-400">Email:</span> {{ auth()->user()->email }}</p>
                <p><span class="text-gray-600 dark:text-gray-400">Phone:</span> {{ auth()->user()->phone ?? 'Not set' }}</p>
                <p><span class="text-gray-600 dark:text-gray-400">Member since:</span> {{ auth()->user()->created_at->format('M Y') }}</p>
            </div>
            <div class="mt-4">
                <a href="{{ route('settings.profile') }}" wire:navigate 
                   class="text-blue-600 hover:text-blue-800 text-sm">
                    Edit Profile
                </a>
            </div>
        </div>
    </div>
</div>
