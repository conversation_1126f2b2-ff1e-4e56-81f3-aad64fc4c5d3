<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Welcome, {{ auth()->user()->name }}!</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Quick Links -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Links</h2>
            <div class="space-y-3">
                <a href="{{ route('products.index') }}" wire:navigate 
                   class="block text-blue-600 hover:text-blue-800 text-sm">
                    Browse Products
                </a>
                <a href="{{ route('orders.index') }}" wire:navigate 
                   class="block text-blue-600 hover:text-blue-800 text-sm">
                    My Orders
                </a>
                <a href="{{ route('wishlist') }}" wire:navigate 
                   class="block text-blue-600 hover:text-blue-800 text-sm">
                    My Wishlist
                </a>
                <a href="{{ route('account.addresses') }}" wire:navigate 
                   class="block text-blue-600 hover:text-blue-800 text-sm">
                    Address Book
                </a>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Orders</h2>
            @if($this->recentOrders->count() > 0)
                <div class="space-y-3">
                    @foreach($this->recentOrders as $order)
                        <div class="border-b border-gray-200 dark:border-gray-700 pb-2 last:border-b-0">
                            <a href="{{ route('orders.show', $order) }}" wire:navigate 
                               class="text-sm font-medium text-blue-600 hover:text-blue-800">
                                #{{ $order->order_number }}
                            </a>
                            <p class="text-xs text-gray-600 dark:text-gray-400">
                                {{ $order->created_at->format('M d, Y') }} - 
                                Rp {{ number_format($order->total_amount, 0, ',', '.') }}
                            </p>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-gray-600 dark:text-gray-400 text-sm">No orders yet</p>
            @endif
        </div>

        <!-- Account Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Account</h2>
            <div class="space-y-2 text-sm">
                <p><span class="text-gray-600 dark:text-gray-400">Email:</span> {{ auth()->user()->email }}</p>
                <p><span class="text-gray-600 dark:text-gray-400">Phone:</span> {{ auth()->user()->phone ?? 'Not set' }}</p>
                <p><span class="text-gray-600 dark:text-gray-400">Member since:</span> {{ auth()->user()->created_at->format('M Y') }}</p>
            </div>
            <div class="mt-4">
                <a href="{{ route('settings.profile') }}" wire:navigate 
                   class="text-blue-600 hover:text-blue-800 text-sm">
                    Edit Profile
                </a>
            </div>
        </div>
    </div>
</div>
