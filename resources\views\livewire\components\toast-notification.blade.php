<div class="fixed top-4 right-4 z-50 space-y-2" x-data="{ notifications: @entangle('notifications') }">
    @foreach($notifications as $notification)
        <div 
            x-data="{ show: false, id: '{{ $notification['id'] }}' }"
            x-init="
                show = true;
                setTimeout(() => {
                    show = false;
                    setTimeout(() => $wire.removeNotification(id), 300);
                }, {{ $notification['duration'] }});
            "
            x-show="show"
            x-transition:enter="transform ease-out duration-300 transition"
            x-transition:enter-start="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
            x-transition:enter-end="translate-y-0 opacity-100 sm:translate-x-0"
            x-transition:leave="transition ease-in duration-100"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            class="max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
        >
            <div class="p-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        @if($notification['type'] === 'success')
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                                <flux:icon name="check" class="h-4 w-4 text-green-600" />
                            </div>
                        @elseif($notification['type'] === 'error')
                            <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                                <flux:icon name="x-mark" class="h-4 w-4 text-red-600" />
                            </div>
                        @elseif($notification['type'] === 'warning')
                            <div class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
                                <flux:icon name="exclamation-triangle" class="h-4 w-4 text-yellow-600" />
                            </div>
                        @else
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                                <flux:icon name="information-circle" class="h-4 w-4 text-blue-600" />
                            </div>
                        @endif
                    </div>
                    <div class="ml-3 w-0 flex-1 pt-0.5">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $notification['message'] }}
                        </p>
                    </div>
                    <div class="ml-4 flex-shrink-0 flex">
                        <button 
                            @click="show = false; setTimeout(() => $wire.removeNotification(id), 300)"
                            class="bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            <flux:icon name="x-mark" class="h-5 w-5" />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endforeach
</div>
