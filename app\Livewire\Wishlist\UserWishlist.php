<?php

namespace App\Livewire\Wishlist;

use App\Models\Cart;
use App\Models\Wishlist;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

class UserWishlist extends Component
{
    public $wishlistItems = [];

    public function mount()
    {
        if (!Auth::check()) {
            return $this->redirect(route('login'));
        }
        
        $this->loadWishlistItems();
    }

    #[On('wishlist-updated')]
    public function loadWishlistItems()
    {
        $this->wishlistItems = Wishlist::with('product')
            ->where('user_id', Auth::id())
            ->get();
    }

    public function removeFromWishlist($wishlistItemId)
    {
        Wishlist::find($wishlistItemId)?->delete();
        $this->loadWishlistItems();
        $this->dispatch('wishlist-updated');
        session()->flash('success', 'Item removed from wishlist.');
    }

    public function moveToCart($wishlistItemId)
    {
        $wishlistItem = Wishlist::with('product')->find($wishlistItemId);
        if (!$wishlistItem) {
            return;
        }

        $product = $wishlistItem->product;

        // Check if product is in stock
        if ($product->stock_quantity <= 0) {
            session()->flash('error', 'Product is out of stock.');
            return;
        }

        // Check if already in cart
        $existingCartItem = Cart::where('user_id', Auth::id())
            ->where('product_id', $product->id)
            ->whereNull('product_variation_id')
            ->first();

        if ($existingCartItem) {
            $existingCartItem->update([
                'quantity' => $existingCartItem->quantity + 1,
                'price' => $product->effective_price,
            ]);
        } else {
            Cart::create([
                'user_id' => Auth::id(),
                'product_id' => $product->id,
                'quantity' => 1,
                'price' => $product->effective_price,
            ]);
        }

        // Remove from wishlist
        $wishlistItem->delete();
        
        $this->loadWishlistItems();
        $this->dispatch('cart-updated');
        $this->dispatch('wishlist-updated');
        
        session()->flash('success', 'Item moved to cart successfully!');
    }

    public function clearWishlist()
    {
        Wishlist::where('user_id', Auth::id())->delete();
        $this->loadWishlistItems();
        $this->dispatch('wishlist-updated');
        session()->flash('success', 'Wishlist cleared.');
    }

    #[Layout('layouts.app')]
    #[Title('My Wishlist')]
    public function render()
    {
        return view('livewire.wishlist.user-wishlist');
    }
}
