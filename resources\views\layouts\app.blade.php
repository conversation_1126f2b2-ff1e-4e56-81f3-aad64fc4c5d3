<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ isset($title) ? $title . ' - ' : '' }}{{ config('app.name', 'SportZone') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @fluxStyles
</head>
<body class="font-sans antialiased h-full bg-gray-50 dark:bg-gray-900">
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo and Main Navigation -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <a href="{{ route('products.index') }}" wire:navigate class="text-xl font-bold text-gray-900 dark:text-white">
                            SportZone
                        </a>
                    </div>
                    <div class="hidden md:ml-8 md:flex md:space-x-8">
                        <a href="{{ route('products.index') }}" wire:navigate 
                           class="text-gray-900 dark:text-white hover:text-blue-600 px-3 py-2 text-sm font-medium">
                            Products
                        </a>
                        <a href="{{ route('products.index', ['category' => 'football']) }}" wire:navigate 
                           class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-3 py-2 text-sm font-medium">
                            Football
                        </a>
                        <a href="{{ route('products.index', ['category' => 'basketball']) }}" wire:navigate 
                           class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-3 py-2 text-sm font-medium">
                            Basketball
                        </a>
                        <a href="{{ route('products.index', ['category' => 'running']) }}" wire:navigate 
                           class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-3 py-2 text-sm font-medium">
                            Running
                        </a>
                        <a href="{{ route('products.index', ['category' => 'fitness']) }}" wire:navigate 
                           class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-3 py-2 text-sm font-medium">
                            Fitness
                        </a>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="flex-1 max-w-lg mx-8 hidden lg:block">
                    <livewire:components.search-bar />
                </div>

                <!-- Right Navigation -->
                <div class="flex items-center space-x-4">
                    <!-- Cart -->
                    <livewire:components.cart-counter />

                    @auth
                        <!-- Wishlist -->
                        <a href="{{ route('wishlist') }}" wire:navigate class="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                            <flux:icon name="heart" class="h-6 w-6" />
                        </a>

                        <!-- User Menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <div class="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        {{ substr(auth()->user()->name, 0, 1) }}
                                    </span>
                                </div>
                            </button>

                            <div x-show="open" @click.away="open = false" 
                                 class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50">
                                <a href="{{ route('dashboard') }}" wire:navigate 
                                   class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    Dashboard
                                </a>
                                <a href="{{ route('orders.index') }}" wire:navigate 
                                   class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    My Orders
                                </a>
                                <a href="{{ route('wishlist') }}" wire:navigate 
                                   class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    Wishlist
                                </a>
                                <a href="{{ route('account.addresses') }}" wire:navigate
                                   class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    Address Book
                                </a>
                                <a href="{{ route('settings.profile') }}" wire:navigate 
                                   class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    Settings
                                </a>
                                <div class="border-t border-gray-100 dark:border-gray-700"></div>
                                <livewire:actions.logout class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" />
                            </div>
                        </div>
                    @else
                        <a href="{{ route('login') }}" wire:navigate 
                           class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-3 py-2 text-sm font-medium">
                            Login
                        </a>
                        <a href="{{ route('register') }}" wire:navigate 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Sign Up
                        </a>
                    @endauth

                    <!-- Mobile menu button -->
                    <div class="md:hidden">
                        <button @click="mobileMenuOpen = !mobileMenuOpen" class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                            <flux:icon name="bars-3" class="h-6 w-6" />
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="md:hidden" x-data="{ mobileMenuOpen: false }" x-show="mobileMenuOpen" @click.away="mobileMenuOpen = false">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                <a href="{{ route('products.index') }}" wire:navigate 
                   class="text-gray-900 dark:text-white block px-3 py-2 text-base font-medium">
                    Products
                </a>
                <a href="{{ route('products.index', ['category' => 'football']) }}" wire:navigate 
                   class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white block px-3 py-2 text-base font-medium">
                    Football
                </a>
                <a href="{{ route('products.index', ['category' => 'basketball']) }}" wire:navigate 
                   class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white block px-3 py-2 text-base font-medium">
                    Basketball
                </a>
                <a href="{{ route('products.index', ['category' => 'running']) }}" wire:navigate 
                   class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white block px-3 py-2 text-base font-medium">
                    Running
                </a>
                <a href="{{ route('products.index', ['category' => 'fitness']) }}" wire:navigate 
                   class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white block px-3 py-2 text-base font-medium">
                    Fitness
                </a>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    @if (session()->has('success'))
        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
            <span class="block sm:inline">{{ session('success') }}</span>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    @if (session()->has('info'))
        <div class="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
            <span class="block sm:inline">{{ session('info') }}</span>
        </div>
    @endif

    <!-- Main Content -->
    <main class="min-h-screen">
        {{ $slot }}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">SportZone</h3>
                    <p class="text-gray-300">Your one-stop shop for all sports equipment and gear.</p>
                </div>
                <div>
                    <h4 class="text-md font-semibold mb-4">Categories</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="{{ route('products.index', ['category' => 'football']) }}" wire:navigate class="hover:text-white">Football</a></li>
                        <li><a href="{{ route('products.index', ['category' => 'basketball']) }}" wire:navigate class="hover:text-white">Basketball</a></li>
                        <li><a href="{{ route('products.index', ['category' => 'running']) }}" wire:navigate class="hover:text-white">Running</a></li>
                        <li><a href="{{ route('products.index', ['category' => 'fitness']) }}" wire:navigate class="hover:text-white">Fitness</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-md font-semibold mb-4">Customer Service</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#" class="hover:text-white">Contact Us</a></li>
                        <li><a href="#" class="hover:text-white">Shipping Info</a></li>
                        <li><a href="#" class="hover:text-white">Returns</a></li>
                        <li><a href="#" class="hover:text-white">Size Guide</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-md font-semibold mb-4">Connect</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#" class="hover:text-white">Facebook</a></li>
                        <li><a href="#" class="hover:text-white">Instagram</a></li>
                        <li><a href="#" class="hover:text-white">Twitter</a></li>
                        <li><a href="#" class="hover:text-white">YouTube</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
                <p>&copy; {{ date('Y') }} SportZone. All rights reserved.</p>
            </div>
        </div>
    </footer>

    @fluxScripts
    <script src="//unpkg.com/alpinejs" defer></script>
</body>
</html>
