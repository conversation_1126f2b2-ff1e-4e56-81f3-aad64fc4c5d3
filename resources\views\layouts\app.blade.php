<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ isset($title) ? "{$title} - " : '' }}{{ config('app.name', 'SportZone') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @fluxStyles
</head>
<body class="font-sans antialiased h-full bg-gray-50 dark:bg-gray-900">
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 backdrop-blur-sm bg-white/95 dark:bg-gray-800/95" x-data="{ mobileMenuOpen: false }">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo and Main Navigation -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <a href="{{ route('products.index') }}" wire:navigate class="flex items-center group">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center mr-3 shadow-md group-hover:shadow-lg transition-all duration-200">
                                <flux:icon name="bolt" class="h-6 w-6 text-white" />
                            </div>
                            <span class="text-xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">SportZone</span>
                        </a>
                    </div>
                    <div class="hidden md:ml-8 md:flex md:space-x-1">
                        <a href="{{ route('products.index') }}" wire:navigate
                           class="text-gray-900 dark:text-white px-4 py-2 text-sm font-medium rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            Produk
                        </a>
                        <div class="relative group">
                            <button class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-4 py-2 text-sm font-medium rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 flex items-center">
                                Kategori
                                <flux:icon name="chevron-down" class="h-4 w-4 ml-1" />
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                                <div class="py-2">
                                    <a href="{{ route('products.index', ['category' => 'football']) }}" wire:navigate
                                       class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                        ⚽ Sepak Bola
                                    </a>
                                    <a href="{{ route('products.index', ['category' => 'basketball']) }}" wire:navigate
                                       class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                        🏀 Basket
                                    </a>
                                    <a href="{{ route('products.index', ['category' => 'running']) }}" wire:navigate
                                       class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                        🏃 Lari
                                    </a>
                                    <a href="{{ route('products.index', ['category' => 'fitness']) }}" wire:navigate
                                       class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                        💪 Fitness
                                    </a>
                                </div>
                            </div>
                        </div>
                        <a href="{{ route('cart') }}" wire:navigate
                           class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-4 py-2 text-sm font-medium rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            Keranjang
                        </a>
                        <a href="{{ route('wishlist') }}" wire:navigate
                           class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-4 py-2 text-sm font-medium rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            Wishlist
                        </a>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="flex-1 max-w-lg mx-8 hidden lg:block">
                    <livewire:components.search-bar />
                </div>

                <!-- Right Navigation -->
                <div class="flex items-center space-x-3">
                    <!-- Cart -->
                    <livewire:components.cart-counter />

                    @auth
                        <!-- Wishlist -->
                        <a href="{{ route('wishlist') }}" wire:navigate class="relative p-2 text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200">
                            <flux:icon name="heart" class="h-6 w-6" />
                        </a>

                        <!-- User Menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center space-x-2 text-sm rounded-lg px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                                <div class="h-8 w-8 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-md">
                                    <span class="text-sm font-semibold text-white">
                                        {{ substr(auth()->user()->name, 0, 1) }}
                                    </span>
                                </div>
                                <span class="hidden md:block text-gray-700 dark:text-gray-300 font-medium">{{ auth()->user()->name }}</span>
                                <flux:icon name="chevron-down" class="h-4 w-4 text-gray-500 dark:text-gray-400" />
                            </button>

                            <div x-show="open" @click.away="open = false"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95"
                                 class="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-50">

                                <!-- User Info -->
                                <div class="px-4 py-3 border-b border-gray-100 dark:border-gray-700">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ auth()->user()->name }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ auth()->user()->email }}</p>
                                </div>

                                <!-- Menu Items -->
                                <div class="py-1">
                                    <a href="{{ route('dashboard') }}" wire:navigate
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                        <flux:icon name="home" class="h-4 w-4 mr-3" />
                                        Dashboard
                                    </a>
                                    <a href="{{ route('orders.index') }}" wire:navigate
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                        <flux:icon name="shopping-bag" class="h-4 w-4 mr-3" />
                                        Pesanan Saya
                                    </a>
                                    <a href="{{ route('wishlist') }}" wire:navigate
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                        <flux:icon name="heart" class="h-4 w-4 mr-3" />
                                        Wishlist
                                    </a>
                                    <a href="{{ route('account.addresses') }}" wire:navigate
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                        <flux:icon name="map-pin" class="h-4 w-4 mr-3" />
                                        Buku Alamat
                                    </a>
                                    <a href="{{ route('settings.profile') }}" wire:navigate
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                        <flux:icon name="cog-6-tooth" class="h-4 w-4 mr-3" />
                                        Pengaturan
                                    </a>
                                </div>

                                <div class="border-t border-gray-100 dark:border-gray-700 pt-1">
                                    <livewire:actions.logout class="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200" />
                                </div>
                            </div>
                        </div>
                    @else
                        <a href="{{ route('login') }}" wire:navigate 
                           class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-3 py-2 text-sm font-medium">
                            Login
                        </a>
                        <a href="{{ route('register') }}" wire:navigate 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Sign Up
                        </a>
                    @endauth

                    <!-- Mobile menu button -->
                    <div class="md:hidden">
                        <button @click="mobileMenuOpen = !mobileMenuOpen" class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            <flux:icon name="bars-3" class="h-6 w-6" x-show="!mobileMenuOpen" />
                            <flux:icon name="x-mark" class="h-6 w-6" x-show="mobileMenuOpen" />
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="md:hidden" x-show="mobileMenuOpen" @click.away="mobileMenuOpen = false"
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="transition ease-in duration-75"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                <a href="{{ route('products.index') }}" wire:navigate class="block px-3 py-2 text-base font-medium text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md">
                    Produk
                </a>
                <a href="{{ route('cart') }}" wire:navigate class="block px-3 py-2 text-base font-medium text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md">
                    Keranjang
                </a>
                <a href="{{ route('wishlist') }}" wire:navigate class="block px-3 py-2 text-base font-medium text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md">
                    Wishlist
                </a>

                <!-- Categories -->
                <div class="pt-2">
                    <div class="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Kategori
                    </div>
                    <a href="{{ route('products.index', ['category' => 'football']) }}" wire:navigate class="block px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md">
                        ⚽ Sepak Bola
                    </a>
                    <a href="{{ route('products.index', ['category' => 'basketball']) }}" wire:navigate class="block px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md">
                        🏀 Basket
                    </a>
                    <a href="{{ route('products.index', ['category' => 'running']) }}" wire:navigate class="block px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md">
                        🏃 Lari
                    </a>
                    <a href="{{ route('products.index', ['category' => 'fitness']) }}" wire:navigate class="block px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md">
                        💪 Fitness
                    </a>
                </div>
            </div>
                <a href="{{ route('products.index') }}" wire:navigate 
                   class="text-gray-900 dark:text-white block px-3 py-2 text-base font-medium">
                    Products
                </a>
                <a href="{{ route('products.index', ['category' => 'football']) }}" wire:navigate 
                   class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white block px-3 py-2 text-base font-medium">
                    Football
                </a>
                <a href="{{ route('products.index', ['category' => 'basketball']) }}" wire:navigate 
                   class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white block px-3 py-2 text-base font-medium">
                    Basketball
                </a>
                <a href="{{ route('products.index', ['category' => 'running']) }}" wire:navigate 
                   class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white block px-3 py-2 text-base font-medium">
                    Running
                </a>
                <a href="{{ route('products.index', ['category' => 'fitness']) }}" wire:navigate 
                   class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white block px-3 py-2 text-base font-medium">
                    Fitness
                </a>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    @if (session()->has('success'))
        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
            <span class="block sm:inline">{{ session('success') }}</span>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    @if (session()->has('info'))
        <div class="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
            <span class="block sm:inline">{{ session('info') }}</span>
        </div>
    @endif

    <!-- Main Content -->
    <main class="min-h-screen">
        {{ $slot }}
    </main>

    <!-- Toast Notifications -->
    <livewire:components.toast-notification />

    <!-- Notifications -->
    <x-notification />

    <!-- Footer -->
    <footer class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
        <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-1">
                    <div class="flex items-center mb-6">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                            <flux:icon name="bolt" class="h-6 w-6 text-white" />
                        </div>
                        <h3 class="text-2xl font-bold bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent">SportZone</h3>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed">Destinasi utama Anda untuk peralatan olahraga berkualitas tinggi. Tingkatkan permainan Anda dengan produk-produk profesional kami.</p>

                    <!-- Social Media -->
                    <div class="flex space-x-3">
                        <x-social-icon type="facebook" href="https://facebook.com/sportzone" />
                        <x-social-icon type="twitter" href="https://twitter.com/sportzone" />
                        <x-social-icon type="instagram" href="https://instagram.com/sportzone" />
                        <x-social-icon type="youtube" href="https://youtube.com/sportzone" />
                    </div>
                </div>

                <!-- Categories -->
                <div>
                    <h4 class="text-lg font-semibold mb-6 text-blue-400">Kategori</h4>
                    <ul class="space-y-3">
                        <li><a href="{{ route('products.index', ['category' => 'football']) }}" wire:navigate class="text-gray-300 hover:text-blue-400 transition-colors duration-200 flex items-center">
                            <flux:icon name="chevron-right" class="h-4 w-4 mr-2" />
                            Sepak Bola
                        </a></li>
                        <li><a href="{{ route('products.index', ['category' => 'basketball']) }}" wire:navigate class="text-gray-300 hover:text-blue-400 transition-colors duration-200 flex items-center">
                            <flux:icon name="chevron-right" class="h-4 w-4 mr-2" />
                            Basket
                        </a></li>
                        <li><a href="{{ route('products.index', ['category' => 'running']) }}" wire:navigate class="text-gray-300 hover:text-blue-400 transition-colors duration-200 flex items-center">
                            <flux:icon name="chevron-right" class="h-4 w-4 mr-2" />
                            Lari
                        </a></li>
                        <li><a href="{{ route('products.index', ['category' => 'fitness']) }}" wire:navigate class="text-gray-300 hover:text-blue-400 transition-colors duration-200 flex items-center">
                            <flux:icon name="chevron-right" class="h-4 w-4 mr-2" />
                            Fitness
                        </a></li>
                    </ul>
                </div>

                <!-- Customer Service -->
                <div>
                    <h4 class="text-lg font-semibold mb-6 text-blue-400">Layanan Pelanggan</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-300 hover:text-blue-400 transition-colors duration-200 flex items-center">
                            <flux:icon name="chevron-right" class="h-4 w-4 mr-2" />
                            Hubungi Kami
                        </a></li>
                        <li><a href="#" class="text-gray-300 hover:text-blue-400 transition-colors duration-200 flex items-center">
                            <flux:icon name="chevron-right" class="h-4 w-4 mr-2" />
                            Info Pengiriman
                        </a></li>
                        <li><a href="#" class="text-gray-300 hover:text-blue-400 transition-colors duration-200 flex items-center">
                            <flux:icon name="chevron-right" class="h-4 w-4 mr-2" />
                            Retur & Tukar
                        </a></li>
                        <li><a href="#" class="text-gray-300 hover:text-blue-400 transition-colors duration-200 flex items-center">
                            <flux:icon name="chevron-right" class="h-4 w-4 mr-2" />
                            Panduan Ukuran
                        </a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-md font-semibold mb-4">Connect</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#" class="hover:text-white">Facebook</a></li>
                        <li><a href="#" class="hover:text-white">Instagram</a></li>
                        <li><a href="#" class="hover:text-white">Twitter</a></li>
                        <li><a href="#" class="hover:text-white">YouTube</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
                <p>&copy; {{ date('Y') }} SportZone. All rights reserved.</p>
            </div>
        </div>
    </footer>

    @fluxScripts
    <script src="//unpkg.com/alpinejs" defer></script>
</body>
</html>
