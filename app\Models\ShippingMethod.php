<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShippingMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
        'base_cost',
        'cost_per_kg',
        'estimated_days_min',
        'estimated_days_max',
        'free_shipping_threshold',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'base_cost' => 'decimal:2',
            'cost_per_kg' => 'decimal:2',
            'free_shipping_threshold' => 'decimal:2',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Calculate shipping cost
     */
    public function calculateCost($weight = 0, $orderTotal = 0)
    {
        // Check for free shipping
        if ($this->free_shipping_threshold && $orderTotal >= $this->free_shipping_threshold) {
            return 0;
        }

        $cost = $this->base_cost;
        
        if ($weight > 0) {
            $cost += ($weight * $this->cost_per_kg);
        }

        return $cost;
    }

    /**
     * Get estimated delivery range
     */
    public function getEstimatedDeliveryAttribute()
    {
        if ($this->estimated_days_min === $this->estimated_days_max) {
            return $this->estimated_days_min . ' hari';
        }

        return $this->estimated_days_min . '-' . $this->estimated_days_max . ' hari';
    }

    /**
     * Scope for active shipping methods
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
