<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8 text-center">
        <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent mb-4">Peralatan Olahraga</h1>
        <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">Temukan berbagai macam peralatan olahraga berkualitas untuk semua kebutuhan atletik Anda</p>

        <!-- Stats -->
        <div class="flex justify-center space-x-8 mt-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ $this->products->total() }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Produk</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ $categories->count() }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Kategori</div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <!-- Left Side - Search and Filters -->
            <div class="flex flex-col sm:flex-row gap-4 flex-1">
                <!-- Search -->
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <flux:icon name="magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                        <flux:input
                            wire:model.live.debounce.300ms="search"
                            placeholder="Cari produk..."
                            type="search"
                            class="pl-10"
                        />
                    </div>
                </div>

                <!-- Category -->
                <div class="min-w-48">
                    <flux:select wire:model.live="category" placeholder="Semua Kategori">
                        <option value="">Semua Kategori</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->slug }}">{{ $category->name }}</option>
                            @foreach($category->children as $child)
                                <option value="{{ $child->slug }}">-- {{ $child->name }}</option>
                            @endforeach
                        @endforeach
                    </flux:select>
                </div>
            </div>

            <!-- Brand -->
            <div>
                <flux:select wire:model.live="brand" placeholder="All Brands">
                    <option value="">All Brands</option>
                    @foreach($brands as $brandName)
                        <option value="{{ $brandName }}">{{ $brandName }}</option>
                    @endforeach
                </flux:select>
            </div>

            <!-- Price Range -->
            <div class="flex gap-2">
                <flux:input 
                    wire:model.live.debounce.500ms="minPrice" 
                    placeholder="Min Price" 
                    type="number"
                    min="0"
                />
                <flux:input 
                    wire:model.live.debounce.500ms="maxPrice" 
                    placeholder="Max Price" 
                    type="number"
                    min="0"
                />
            </div>

            <!-- Sort -->
            <div>
                <flux:select wire:model.live="sort">
                    <option value="name">Name A-Z</option>
                    <option value="price_low">Price: Low to High</option>
                    <option value="price_high">Price: High to Low</option>
                    <option value="rating">Highest Rated</option>
                    <option value="newest">Newest</option>
                </flux:select>
            </div>
        </div>

        @if($search || $category || $brand || $minPrice || $maxPrice)
            <div class="mt-4">
                <flux:button wire:click="clearFilters" variant="outline" size="sm">
                    Clear Filters
                </flux:button>
            </div>
        @endif
    </div>

    <!-- Products Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        @forelse($products as $product)
            <div class="group bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-xl hover:-translate-y-1 transition-all duration-300">
                <!-- Product Image -->
                <div class="aspect-square bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 relative overflow-hidden">
                    @if($product->images && count($product->images) > 0)
                        <img src="{{ $product->images[0] }}" alt="{{ $product->name }}"
                             class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                    @else
                        <div class="w-full h-full flex items-center justify-center text-gray-400">
                            <flux:icon name="photo" class="w-16 h-16" />
                        </div>
                    @endif

                    <!-- Badges -->
                    <div class="absolute top-3 left-3 flex flex-col gap-2">
                        @if($product->is_on_sale)
                            <div class="bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                                -{{ $product->discount_percentage }}%
                            </div>
                        @endif

                        @if($product->is_featured)
                            <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                                ⭐ Featured
                            </div>
                        @endif
                    </div>

                    <!-- Quick Actions -->
                    <div class="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <livewire:components.wishlist-toggle :product="$product" :key="'wishlist-'.$product->id" />
                        <a href="{{ route('products.show', $product) }}" wire:navigate
                           class="p-2 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 group">
                            <flux:icon name="eye" class="h-4 w-4 text-gray-600 dark:text-gray-400 group-hover:text-blue-600 transition-colors duration-200" />
                        </a>
                    </div>
                </div>

                <!-- Product Info -->
                <div class="p-6">
                    <div class="mb-3">
                        <h3 class="font-bold text-lg text-gray-900 dark:text-white mb-1 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200">
                            <a href="{{ route('products.show', $product) }}" wire:navigate>
                                {{ $product->name }}
                            </a>
                        </h3>

                        @if($product->brand)
                            <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">{{ $product->brand }}</p>
                        @endif
                    </div>

                    <!-- Rating -->
                    <div class="flex items-center mb-4">
                        <div class="flex items-center">
                            @for($i = 1; $i <= 5; $i++)
                                <flux:icon name="star" class="w-4 h-4 {{ $i <= $product->rating ? 'text-yellow-400 fill-current' : 'text-gray-300' }}" />
                            @endfor
                        </div>
                        <span class="text-sm text-gray-600 dark:text-gray-400 ml-2">{{ $product->rating }} ({{ $product->review_count }})</span>
                    </div>

                    <!-- Price -->
                    <div class="mb-4">
                        @if($product->is_on_sale)
                            <div class="flex items-center gap-2">
                                <span class="text-xl font-bold text-blue-600 dark:text-blue-400">
                                    Rp {{ number_format($product->sale_price, 0, ',', '.') }}
                                </span>
                                <span class="text-sm text-gray-500 line-through">
                                    Rp {{ number_format($product->price, 0, ',', '.') }}
                                </span>
                            </div>
                        @else
                            <span class="text-xl font-bold text-gray-900 dark:text-white">
                                Rp {{ number_format($product->price, 0, ',', '.') }}
                            </span>
                        @endif
                    </div>

                    <!-- Add to Cart Component -->
                    <livewire:components.add-to-cart :product="$product" :key="'cart-'.$product->id" />

                    <!-- Stock Status -->
                    @if($product->stock_quantity <= 5 && $product->stock_quantity > 0)
                        <div class="mt-3 text-xs text-orange-600 dark:text-orange-400 font-medium">
                            Hanya tersisa {{ $product->stock_quantity }} stok!
                        </div>
                    @elseif($product->stock_quantity == 0)
                        <div class="mt-3 text-xs text-red-600 dark:text-red-400 font-medium">
                            Stok habis
                        </div>
                    @endif
                </div>
            </div>
        @empty
            <div class="col-span-full">
                <div class="text-center py-16 bg-white dark:bg-gray-800 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600">
                    <div class="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                        <flux:icon name="shopping-bag" class="w-12 h-12 text-gray-400" />
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Produk tidak ditemukan</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
                        Kami tidak dapat menemukan produk yang sesuai dengan kriteria Anda. Coba sesuaikan pencarian atau opsi filter Anda.
                    </p>
                    @if($search || $category || $brand || $minPrice || $maxPrice)
                        <flux:button wire:click="clearFilters" variant="outline">
                            <flux:icon name="x-mark" class="h-4 w-4 mr-2" />
                            Hapus Semua Filter
                        </flux:button>
                    @endif
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    <div class="flex justify-center">
        {{ $products->links() }}
    </div>
</div>
