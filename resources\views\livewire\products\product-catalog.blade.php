<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Sports Equipment</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">Discover our wide range of quality sports equipment</p>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <!-- Search -->
            <div>
                <flux:input 
                    wire:model.live.debounce.300ms="search" 
                    placeholder="Search products..." 
                    type="search"
                />
            </div>

            <!-- Category -->
            <div>
                <flux:select wire:model.live="category" placeholder="All Categories">
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->slug }}">{{ $category->name }}</option>
                        @foreach($category->children as $child)
                            <option value="{{ $child->slug }}">-- {{ $child->name }}</option>
                        @endforeach
                    @endforeach
                </flux:select>
            </div>

            <!-- Brand -->
            <div>
                <flux:select wire:model.live="brand" placeholder="All Brands">
                    <option value="">All Brands</option>
                    @foreach($brands as $brandName)
                        <option value="{{ $brandName }}">{{ $brandName }}</option>
                    @endforeach
                </flux:select>
            </div>

            <!-- Price Range -->
            <div class="flex gap-2">
                <flux:input 
                    wire:model.live.debounce.500ms="minPrice" 
                    placeholder="Min Price" 
                    type="number"
                    min="0"
                />
                <flux:input 
                    wire:model.live.debounce.500ms="maxPrice" 
                    placeholder="Max Price" 
                    type="number"
                    min="0"
                />
            </div>

            <!-- Sort -->
            <div>
                <flux:select wire:model.live="sort">
                    <option value="name">Name A-Z</option>
                    <option value="price_low">Price: Low to High</option>
                    <option value="price_high">Price: High to Low</option>
                    <option value="rating">Highest Rated</option>
                    <option value="newest">Newest</option>
                </flux:select>
            </div>
        </div>

        @if($search || $category || $brand || $minPrice || $maxPrice)
            <div class="mt-4">
                <flux:button wire:click="clearFilters" variant="outline" size="sm">
                    Clear Filters
                </flux:button>
            </div>
        @endif
    </div>

    <!-- Products Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        @forelse($products as $product)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                <!-- Product Image -->
                <div class="aspect-square bg-gray-100 dark:bg-gray-700 relative">
                    @if($product->images && count($product->images) > 0)
                        <img src="{{ $product->images[0] }}" alt="{{ $product->name }}" 
                             class="w-full h-full object-cover">
                    @else
                        <div class="w-full h-full flex items-center justify-center text-gray-400">
                            <flux:icon name="photo" class="w-16 h-16" />
                        </div>
                    @endif
                    
                    @if($product->is_on_sale)
                        <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-medium">
                            -{{ $product->discount_percentage }}%
                        </div>
                    @endif

                    @if($product->is_featured)
                        <div class="absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded text-sm font-medium">
                            Featured
                        </div>
                    @endif
                </div>

                <!-- Product Info -->
                <div class="p-4">
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-1 line-clamp-2">
                        <a href="{{ route('products.show', $product) }}" wire:navigate class="hover:text-blue-600">
                            {{ $product->name }}
                        </a>
                    </h3>
                    
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{{ $product->brand }}</p>
                    
                    <div class="flex items-center mb-2">
                        @for($i = 1; $i <= 5; $i++)
                            <flux:icon name="star" class="w-4 h-4 {{ $i <= $product->rating ? 'text-yellow-400' : 'text-gray-300' }}" />
                        @endfor
                        <span class="text-sm text-gray-600 dark:text-gray-400 ml-1">({{ $product->review_count }})</span>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            @if($product->is_on_sale)
                                <span class="text-lg font-bold text-gray-900 dark:text-white">
                                    Rp {{ number_format($product->sale_price, 0, ',', '.') }}
                                </span>
                                <span class="text-sm text-gray-500 line-through ml-1">
                                    Rp {{ number_format($product->price, 0, ',', '.') }}
                                </span>
                            @else
                                <span class="text-lg font-bold text-gray-900 dark:text-white">
                                    Rp {{ number_format($product->price, 0, ',', '.') }}
                                </span>
                            @endif
                        </div>
                        
                        <flux:button size="sm" href="{{ route('products.show', $product) }}" wire:navigate>
                            View
                        </flux:button>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-span-full text-center py-12">
                <flux:icon name="shopping-bag" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No products found</h3>
                <p class="text-gray-600 dark:text-gray-400">Try adjusting your search or filter criteria.</p>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    <div class="flex justify-center">
        {{ $products->links() }}
    </div>
</div>
