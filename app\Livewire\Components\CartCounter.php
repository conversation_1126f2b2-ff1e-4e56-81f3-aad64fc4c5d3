<?php

namespace App\Livewire\Components;

use App\Models\Cart;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;
use Livewire\Component;

class CartCounter extends Component
{
    public $count = 0;

    public function mount()
    {
        $this->updateCount();
    }

    #[On('cart-updated')]
    public function updateCount()
    {
        if (Auth::check()) {
            $this->count = Cart::where('user_id', Auth::id())->sum('quantity');
        } else {
            $this->count = Cart::where('session_id', session()->getId())->sum('quantity');
        }
    }

    public function render()
    {
        return view('livewire.components.cart-counter');
    }
}
