<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">My Wishlist</h1>
        
        @if($wishlistItems->count() > 0)
            <flux:button wire:click="clearWishlist" variant="outline" 
                        wire:confirm="Are you sure you want to clear your wishlist?">
                Clear Wishlist
            </flux:button>
        @endif
    </div>

    @if($wishlistItems->count() > 0)
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            @foreach($wishlistItems as $item)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <!-- Product Image -->
                    <div class="aspect-square bg-gray-100 dark:bg-gray-700 relative">
                        @if($item->product->images && count($item->product->images) > 0)
                            <img src="{{ $item->product->images[0] }}" alt="{{ $item->product->name }}" 
                                 class="w-full h-full object-cover">
                        @else
                            <div class="w-full h-full flex items-center justify-center text-gray-400">
                                <flux:icon name="photo" class="w-16 h-16" />
                            </div>
                        @endif
                        
                        @if($item->product->is_on_sale)
                            <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-medium">
                                -{{ $item->product->discount_percentage }}%
                            </div>
                        @endif

                        <!-- Remove from Wishlist Button -->
                        <button wire:click="removeFromWishlist({{ $item->id }})" 
                                class="absolute top-2 right-2 bg-white dark:bg-gray-800 p-2 rounded-full shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                                wire:confirm="Remove from wishlist?">
                            <flux:icon name="x-mark" class="w-4 h-4 text-gray-600 dark:text-gray-400" />
                        </button>
                    </div>

                    <!-- Product Info -->
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-1 line-clamp-2">
                            <a href="{{ route('products.show', $item->product) }}" wire:navigate 
                               class="hover:text-blue-600">
                                {{ $item->product->name }}
                            </a>
                        </h3>
                        
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{{ $item->product->brand }}</p>
                        
                        <div class="flex items-center mb-3">
                            @for($i = 1; $i <= 5; $i++)
                                <flux:icon name="star" class="w-4 h-4 {{ $i <= $item->product->rating ? 'text-yellow-400' : 'text-gray-300' }}" />
                            @endfor
                            <span class="text-sm text-gray-600 dark:text-gray-400 ml-1">({{ $item->product->review_count }})</span>
                        </div>

                        <!-- Price -->
                        <div class="mb-4">
                            @if($item->product->is_on_sale)
                                <span class="text-lg font-bold text-gray-900 dark:text-white">
                                    Rp {{ number_format($item->product->sale_price, 0, ',', '.') }}
                                </span>
                                <span class="text-sm text-gray-500 line-through ml-1">
                                    Rp {{ number_format($item->product->price, 0, ',', '.') }}
                                </span>
                            @else
                                <span class="text-lg font-bold text-gray-900 dark:text-white">
                                    Rp {{ number_format($item->product->price, 0, ',', '.') }}
                                </span>
                            @endif
                        </div>

                        <!-- Stock Status -->
                        <div class="mb-4">
                            @if($item->product->stock_quantity > 0)
                                <span class="inline-flex items-center text-sm text-green-600">
                                    <flux:icon name="check-circle" class="w-4 h-4 mr-1" />
                                    In Stock
                                </span>
                            @else
                                <span class="inline-flex items-center text-sm text-red-600">
                                    <flux:icon name="x-circle" class="w-4 h-4 mr-1" />
                                    Out of Stock
                                </span>
                            @endif
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-2">
                            @if($item->product->stock_quantity > 0)
                                <flux:button wire:click="moveToCart({{ $item->id }})" variant="primary" size="sm" class="flex-1">
                                    <flux:icon name="shopping-cart" class="w-4 h-4 mr-1" />
                                    Add to Cart
                                </flux:button>
                            @else
                                <flux:button disabled variant="outline" size="sm" class="flex-1">
                                    Out of Stock
                                </flux:button>
                            @endif
                            
                            <flux:button href="{{ route('products.show', $item->product) }}" wire:navigate 
                                        variant="outline" size="sm">
                                View
                            </flux:button>
                        </div>

                        <!-- Added Date -->
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-3">
                            Added {{ $item->created_at->diffForHumans() }}
                        </p>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <!-- Empty Wishlist -->
        <div class="text-center py-12">
            <flux:icon name="heart" class="w-24 h-24 text-gray-400 mx-auto mb-4" />
            <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-2">Your wishlist is empty</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-6">Save items you love to your wishlist for easy access later.</p>
            <flux:button href="{{ route('products.index') }}" wire:navigate variant="primary">
                Start Shopping
            </flux:button>
        </div>
    @endif
</div>
