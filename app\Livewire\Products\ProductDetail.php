<?php

namespace App\Livewire\Products;

use App\Models\Product;
use App\Models\ProductVariation;
use App\Models\Cart;
use App\Models\Wishlist;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Component;

class ProductDetail extends Component
{
    public Product $product;
    public $selectedVariation = null;
    public $quantity = 1;
    public $selectedAttributes = [];

    public function mount(Product $product)
    {
        $this->product = $product->load(['category', 'variations', 'reviews.user']);
        
        // Set default variation if available
        if ($this->product->variations->count() > 0) {
            $this->selectedVariation = $this->product->variations->first()->id;
            $this->selectedAttributes = $this->product->variations->first()->attributes ?? [];
        }
    }

    public function updatedSelectedAttributes()
    {
        // Find matching variation based on selected attributes
        $variation = $this->product->variations->first(function ($variation) {
            return $variation->attributes == $this->selectedAttributes;
        });

        if ($variation) {
            $this->selectedVariation = $variation->id;
        }
    }

    public function addToCart()
    {
        if (!Auth::check()) {
            return $this->redirect(route('login'));
        }

        $variation = null;
        if ($this->selectedVariation) {
            $variation = ProductVariation::find($this->selectedVariation);
            if (!$variation || $variation->stock_quantity < $this->quantity) {
                session()->flash('error', 'Insufficient stock for selected variation.');
                return;
            }
        } elseif ($this->product->stock_quantity < $this->quantity) {
            session()->flash('error', 'Insufficient stock.');
            return;
        }

        $price = $variation ? $variation->effective_price : $this->product->effective_price;

        $existingCartItem = Cart::where('user_id', Auth::id())
            ->where('product_id', $this->product->id)
            ->where('product_variation_id', $this->selectedVariation)
            ->first();

        if ($existingCartItem) {
            $existingCartItem->update([
                'quantity' => $existingCartItem->quantity + $this->quantity,
                'price' => $price,
            ]);
        } else {
            Cart::create([
                'user_id' => Auth::id(),
                'product_id' => $this->product->id,
                'product_variation_id' => $this->selectedVariation,
                'quantity' => $this->quantity,
                'price' => $price,
            ]);
        }

        session()->flash('success', 'Product added to cart successfully!');
        $this->dispatch('cart-updated');
    }

    public function addToWishlist()
    {
        if (!Auth::check()) {
            return $this->redirect(route('login'));
        }

        $exists = Wishlist::where('user_id', Auth::id())
            ->where('product_id', $this->product->id)
            ->exists();

        if ($exists) {
            session()->flash('info', 'Product is already in your wishlist.');
            return;
        }

        Wishlist::create([
            'user_id' => Auth::id(),
            'product_id' => $this->product->id,
        ]);

        session()->flash('success', 'Product added to wishlist!');
        $this->dispatch('wishlist-updated');
    }

    public function removeFromWishlist()
    {
        if (!Auth::check()) {
            return;
        }

        Wishlist::where('user_id', Auth::id())
            ->where('product_id', $this->product->id)
            ->delete();

        session()->flash('success', 'Product removed from wishlist!');
        $this->dispatch('wishlist-updated');
    }

    public function getIsInWishlistProperty()
    {
        if (!Auth::check()) {
            return false;
        }

        return Wishlist::where('user_id', Auth::id())
            ->where('product_id', $this->product->id)
            ->exists();
    }

    public function getCurrentVariationProperty()
    {
        if (!$this->selectedVariation) {
            return null;
        }

        return ProductVariation::find($this->selectedVariation);
    }

    public function getCurrentPriceProperty()
    {
        return $this->currentVariation ? $this->currentVariation->effective_price : $this->product->effective_price;
    }

    public function getCurrentStockProperty()
    {
        return $this->currentVariation ? $this->currentVariation->stock_quantity : $this->product->stock_quantity;
    }

    #[Layout('layouts.app')]
    #[Title('Product Detail')]
    public function render()
    {
        return view('livewire.products.product-detail');
    }
}
