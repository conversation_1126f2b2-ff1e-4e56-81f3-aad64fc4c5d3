<?php

namespace App\Livewire\Account;

use App\Models\Address;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Component;

class AddressBook extends Component
{
    public $addresses = [];
    public $showForm = false;
    public $editingAddress = null;
    
    public $form = [
        'type' => 'shipping',
        'first_name' => '',
        'last_name' => '',
        'company' => '',
        'address_line_1' => '',
        'address_line_2' => '',
        'city' => '',
        'state' => '',
        'postal_code' => '',
        'country' => 'Indonesia',
        'phone' => '',
        'is_default' => false,
    ];

    public function mount()
    {
        $this->loadAddresses();
    }

    public function loadAddresses()
    {
        $this->addresses = Address::where('user_id', Auth::id())
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function addAddress()
    {
        $this->reset('form', 'editingAddress');
        $this->form['type'] = 'shipping';
        $this->showForm = true;
    }

    public function editAddress($addressId)
    {
        $address = Address::where('user_id', Auth::id())->findOrFail($addressId);
        $this->editingAddress = $address;
        $this->form = $address->only([
            'type', 'first_name', 'last_name', 'company', 'address_line_1', 
            'address_line_2', 'city', 'state', 'postal_code', 'country', 'phone', 'is_default'
        ]);
        $this->showForm = true;
    }

    public function saveAddress()
    {
        $this->validate([
            'form.first_name' => 'required|string|max:255',
            'form.last_name' => 'required|string|max:255',
            'form.address_line_1' => 'required|string|max:255',
            'form.city' => 'required|string|max:255',
            'form.state' => 'required|string|max:255',
            'form.postal_code' => 'required|string|max:10',
            'form.phone' => 'required|string|max:20',
            'form.type' => 'required|in:shipping,billing',
        ]);

        // If setting as default, unset other defaults of the same type
        if ($this->form['is_default']) {
            Address::where('user_id', Auth::id())
                ->where('type', $this->form['type'])
                ->update(['is_default' => false]);
        }

        if ($this->editingAddress) {
            $this->editingAddress->update([
                'user_id' => Auth::id(),
                ...$this->form
            ]);
            session()->flash('success', 'Address updated successfully!');
        } else {
            Address::create([
                'user_id' => Auth::id(),
                ...$this->form
            ]);
            session()->flash('success', 'Address added successfully!');
        }

        $this->loadAddresses();
        $this->closeForm();
    }

    public function deleteAddress($addressId)
    {
        $address = Address::where('user_id', Auth::id())->findOrFail($addressId);
        $address->delete();
        
        $this->loadAddresses();
        session()->flash('success', 'Address deleted successfully!');
    }

    public function setAsDefault($addressId)
    {
        $address = Address::where('user_id', Auth::id())->findOrFail($addressId);
        
        // Unset other defaults of the same type
        Address::where('user_id', Auth::id())
            ->where('type', $address->type)
            ->update(['is_default' => false]);
        
        // Set this address as default
        $address->update(['is_default' => true]);
        
        $this->loadAddresses();
        session()->flash('success', 'Default address updated!');
    }

    public function closeForm()
    {
        $this->showForm = false;
        $this->reset('form', 'editingAddress');
    }

    #[Layout('layouts.app')]
    #[Title('Address Book')]
    public function render()
    {
        return view('livewire.account.address-book');
    }
}
