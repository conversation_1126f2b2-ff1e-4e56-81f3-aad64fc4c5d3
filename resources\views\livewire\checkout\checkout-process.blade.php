<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Checkout</h1>

    <!-- Progress Steps -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            @foreach(['Address', 'Shipping', 'Payment', 'Review'] as $index => $stepName)
                <div class="flex items-center {{ $index < 3 ? 'flex-1' : '' }}">
                    <div class="flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
                                {{ $step > $index + 1 ? 'bg-green-500 text-white' : 
                                   ($step == $index + 1 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600') }}">
                        {{ $index + 1 }}
                    </div>
                    <span class="ml-2 text-sm font-medium text-gray-900 dark:text-white">{{ $stepName }}</span>
                    @if($index < 3)
                        <div class="flex-1 h-0.5 mx-4 {{ $step > $index + 1 ? 'bg-green-500' : 'bg-gray-300' }}"></div>
                    @endif
                </div>
            @endforeach
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Step 1: Address -->
            @if($step == 1)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Billing & Shipping Address</h2>
                    
                    <!-- Billing Address -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Billing Address</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @forelse($addresses->where('type', 'billing') as $address)
                                <label class="relative">
                                    <input type="radio" wire:model="selectedBillingAddress" value="{{ $address->id }}" 
                                           class="sr-only peer">
                                    <div class="p-4 border rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20">
                                        <div class="font-medium">{{ $address->full_name }}</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">{{ $address->formatted_address }}</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">{{ $address->phone }}</div>
                                    </div>
                                </label>
                            @empty
                                <p class="text-gray-600 dark:text-gray-400 col-span-2">No billing addresses found.</p>
                            @endforelse
                        </div>
                        <flux:button wire:click="$set('showAddressForm', true); $set('addressForm.type', 'billing')" 
                                    variant="outline" size="sm" class="mt-4">
                            Add New Billing Address
                        </flux:button>
                    </div>

                    <!-- Shipping Address -->
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Shipping Address</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @forelse($addresses->where('type', 'shipping') as $address)
                                <label class="relative">
                                    <input type="radio" wire:model="selectedShippingAddress" value="{{ $address->id }}" 
                                           class="sr-only peer">
                                    <div class="p-4 border rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20">
                                        <div class="font-medium">{{ $address->full_name }}</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">{{ $address->formatted_address }}</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">{{ $address->phone }}</div>
                                    </div>
                                </label>
                            @empty
                                <p class="text-gray-600 dark:text-gray-400 col-span-2">No shipping addresses found.</p>
                            @endforelse
                        </div>
                        <flux:button wire:click="$set('showAddressForm', true); $set('addressForm.type', 'shipping')" 
                                    variant="outline" size="sm" class="mt-4">
                            Add New Shipping Address
                        </flux:button>
                    </div>

                    <!-- Address Form Modal -->
                    @if($showAddressForm)
                        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
                                <h3 class="text-lg font-semibold mb-4">Add New {{ ucfirst($addressForm['type']) }} Address</h3>
                                
                                <div class="space-y-4">
                                    <div class="grid grid-cols-2 gap-4">
                                        <flux:input wire:model="addressForm.first_name" label="First Name" required />
                                        <flux:input wire:model="addressForm.last_name" label="Last Name" required />
                                    </div>
                                    <flux:input wire:model="addressForm.company" label="Company (Optional)" />
                                    <flux:input wire:model="addressForm.address_line_1" label="Address Line 1" required />
                                    <flux:input wire:model="addressForm.address_line_2" label="Address Line 2 (Optional)" />
                                    <div class="grid grid-cols-2 gap-4">
                                        <flux:input wire:model="addressForm.city" label="City" required />
                                        <flux:input wire:model="addressForm.state" label="State/Province" required />
                                    </div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <flux:input wire:model="addressForm.postal_code" label="Postal Code" required />
                                        <flux:input wire:model="addressForm.phone" label="Phone" required />
                                    </div>
                                    <flux:checkbox wire:model="addressForm.is_default" label="Set as default address" />
                                </div>

                                <div class="flex justify-end space-x-2 mt-6">
                                    <flux:button wire:click="$set('showAddressForm', false)" variant="outline">Cancel</flux:button>
                                    <flux:button wire:click="saveAddress" variant="primary">Save Address</flux:button>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="flex justify-end">
                        <flux:button wire:click="nextStep" variant="primary" 
                                    :disabled="!$selectedBillingAddress || !$selectedShippingAddress">
                            Continue to Shipping
                        </flux:button>
                    </div>
                </div>
            @endif

            <!-- Step 2: Shipping -->
            @if($step == 2)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Shipping Method</h2>
                    
                    <div class="space-y-4">
                        @foreach($shippingMethods as $method)
                            <label class="relative">
                                <input type="radio" wire:model="selectedShippingMethod" value="{{ $method->id }}" 
                                       class="sr-only peer">
                                <div class="p-4 border rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="font-medium">{{ $method->name }}</div>
                                            <div class="text-sm text-gray-600 dark:text-gray-400">{{ $method->description }}</div>
                                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                                Estimated delivery: {{ $method->estimated_delivery }}
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            @php
                                                $cost = $method->calculateCost(
                                                    $cartItems->sum(fn($item) => ($item->product->weight ?? 0) * $item->quantity),
                                                    $this->subtotal
                                                );
                                            @endphp
                                            @if($cost > 0)
                                                <div class="font-medium">Rp {{ number_format($cost, 0, ',', '.') }}</div>
                                            @else
                                                <div class="font-medium text-green-600">Free</div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </label>
                        @endforeach
                    </div>

                    <div class="flex justify-between mt-6">
                        <flux:button wire:click="previousStep" variant="outline">Back</flux:button>
                        <flux:button wire:click="nextStep" variant="primary" :disabled="!$selectedShippingMethod">
                            Continue to Payment
                        </flux:button>
                    </div>
                </div>
            @endif

            <!-- Step 3: Payment -->
            @if($step == 3)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Payment Method</h2>
                    
                    <div class="space-y-4 mb-6">
                        <label class="relative">
                            <input type="radio" wire:model="paymentMethod" value="bank_transfer" class="sr-only peer">
                            <div class="p-4 border rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20">
                                <div class="font-medium">Bank Transfer</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Transfer to our bank account</div>
                            </div>
                        </label>
                        
                        <label class="relative">
                            <input type="radio" wire:model="paymentMethod" value="e_wallet" class="sr-only peer">
                            <div class="p-4 border rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20">
                                <div class="font-medium">E-Wallet</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Pay with GoPay, OVO, DANA</div>
                            </div>
                        </label>
                        
                        <label class="relative">
                            <input type="radio" wire:model="paymentMethod" value="credit_card" class="sr-only peer">
                            <div class="p-4 border rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20">
                                <div class="font-medium">Credit Card</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Visa, Mastercard, etc.</div>
                            </div>
                        </label>
                        
                        <label class="relative">
                            <input type="radio" wire:model="paymentMethod" value="cod" class="sr-only peer">
                            <div class="p-4 border rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20">
                                <div class="font-medium">Cash on Delivery (COD)</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Pay when you receive the order</div>
                            </div>
                        </label>
                    </div>

                    <!-- Coupon Code -->
                    <div class="border-t pt-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Coupon Code</h3>
                        @if(!$appliedCoupon)
                            <div class="flex space-x-2">
                                <flux:input wire:model="couponCode" placeholder="Enter coupon code" class="flex-1" />
                                <flux:button wire:click="applyCoupon" variant="outline">Apply</flux:button>
                            </div>
                        @else
                            <div class="flex items-center justify-between bg-green-50 dark:bg-green-900/20 p-3 rounded">
                                <span class="text-green-800 dark:text-green-200">
                                    Coupon "{{ $appliedCoupon->code }}" applied
                                </span>
                                <flux:button wire:click="removeCoupon" variant="outline" size="sm">Remove</flux:button>
                            </div>
                        @endif
                    </div>

                    <div class="flex justify-between mt-6">
                        <flux:button wire:click="previousStep" variant="outline">Back</flux:button>
                        <flux:button wire:click="nextStep" variant="primary">Review Order</flux:button>
                    </div>
                </div>
            @endif

            <!-- Step 4: Review -->
            @if($step == 4)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Review Your Order</h2>
                    
                    <!-- Order Notes -->
                    <div class="mb-6">
                        <flux:textarea wire:model="notes" label="Order Notes (Optional)" 
                                      placeholder="Any special instructions for your order..." />
                    </div>

                    <div class="flex justify-between">
                        <flux:button wire:click="previousStep" variant="outline">Back</flux:button>
                        <flux:button wire:click="placeOrder" variant="primary">Place Order</flux:button>
                    </div>
                </div>
            @endif
        </div>

        <!-- Order Summary Sidebar -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 sticky top-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Order Summary</h2>
                
                <!-- Cart Items -->
                <div class="space-y-3 mb-4 border-b border-gray-200 dark:border-gray-700 pb-4">
                    @foreach($cartItems as $item)
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded overflow-hidden">
                                @if($item->product->images && count($item->product->images) > 0)
                                    <img src="{{ $item->product->images[0] }}" alt="{{ $item->product->name }}" 
                                         class="w-full h-full object-cover">
                                @endif
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    {{ $item->product->name }}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Qty: {{ $item->quantity }}</p>
                            </div>
                            <p class="text-sm font-medium text-gray-900 dark:text-white">
                                Rp {{ number_format($item->price * $item->quantity, 0, ',', '.') }}
                            </p>
                        </div>
                    @endforeach
                </div>

                <!-- Pricing -->
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Subtotal</span>
                        <span class="text-gray-900 dark:text-white">Rp {{ number_format($this->subtotal, 0, ',', '.') }}</span>
                    </div>
                    
                    @if($step >= 2 && $selectedShippingMethod)
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Shipping</span>
                            <span class="text-gray-900 dark:text-white">
                                @if($this->shippingCost > 0)
                                    Rp {{ number_format($this->shippingCost, 0, ',', '.') }}
                                @else
                                    Free
                                @endif
                            </span>
                        </div>
                    @endif
                    
                    @if($appliedCoupon)
                        <div class="flex justify-between text-green-600">
                            <span>Discount</span>
                            <span>-Rp {{ number_format($this->discountAmount, 0, ',', '.') }}</span>
                        </div>
                    @endif
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Tax (11%)</span>
                        <span class="text-gray-900 dark:text-white">Rp {{ number_format($this->taxAmount, 0, ',', '.') }}</span>
                    </div>
                    
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-2">
                        <div class="flex justify-between text-base font-medium">
                            <span class="text-gray-900 dark:text-white">Total</span>
                            <span class="text-gray-900 dark:text-white">Rp {{ number_format($this->total, 0, ',', '.') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
