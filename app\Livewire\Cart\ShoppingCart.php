<?php

namespace App\Livewire\Cart;

use App\Models\Cart;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

class ShoppingCart extends Component
{
    public $cartItems = [];

    public function mount()
    {
        $this->loadCartItems();
    }

    #[On('cart-updated')]
    public function loadCartItems()
    {
        if (Auth::check()) {
            $this->cartItems = Cart::with(['product', 'productVariation'])
                ->where('user_id', Auth::id())
                ->get();
        } else {
            // Handle guest cart with session
            $sessionId = session()->getId();
            $this->cartItems = Cart::with(['product', 'productVariation'])
                ->where('session_id', $sessionId)
                ->get();
        }
    }

    public function updateQuantity($cartItemId, $quantity)
    {
        if ($quantity <= 0) {
            $this->removeItem($cartItemId);
            return;
        }

        $cartItem = Cart::find($cartItemId);
        if (!$cartItem) {
            return;
        }

        // Check stock availability
        $availableStock = $cartItem->productVariation 
            ? $cartItem->productVariation->stock_quantity 
            : $cartItem->product->stock_quantity;

        if ($quantity > $availableStock) {
            session()->flash('error', 'Insufficient stock. Only ' . $availableStock . ' items available.');
            return;
        }

        $cartItem->update(['quantity' => $quantity]);
        $this->loadCartItems();
        $this->dispatch('cart-updated');
    }

    public function removeItem($cartItemId)
    {
        Cart::find($cartItemId)?->delete();
        $this->loadCartItems();
        $this->dispatch('cart-updated');
        session()->flash('success', 'Item removed from cart.');
    }

    public function clearCart()
    {
        if (Auth::check()) {
            Cart::where('user_id', Auth::id())->delete();
        } else {
            Cart::where('session_id', session()->getId())->delete();
        }
        
        $this->loadCartItems();
        $this->dispatch('cart-updated');
        session()->flash('success', 'Cart cleared.');
    }

    public function getSubtotalProperty()
    {
        return $this->cartItems->sum(function ($item) {
            return $item->price * $item->quantity;
        });
    }

    public function getTaxAmountProperty()
    {
        // 11% PPN (Indonesian VAT)
        return $this->subtotal * 0.11;
    }

    public function getTotalProperty()
    {
        return $this->subtotal + $this->taxAmount;
    }

    public function getItemCountProperty()
    {
        return $this->cartItems->sum('quantity');
    }

    public function proceedToCheckout()
    {
        if (!Auth::check()) {
            return $this->redirect(route('login'));
        }

        if ($this->cartItems->isEmpty()) {
            session()->flash('error', 'Your cart is empty.');
            return;
        }

        return $this->redirect(route('checkout'));
    }

    #[Layout('layouts.app')]
    #[Title('Shopping Cart')]
    public function render()
    {
        return view('livewire.cart.shopping-cart');
    }
}
