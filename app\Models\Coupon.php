<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coupon extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'type',
        'value',
        'minimum_amount',
        'maximum_discount',
        'usage_limit',
        'used_count',
        'usage_limit_per_user',
        'starts_at',
        'expires_at',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'value' => 'decimal:2',
            'minimum_amount' => 'decimal:2',
            'maximum_discount' => 'decimal:2',
            'starts_at' => 'datetime',
            'expires_at' => 'datetime',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Check if coupon is valid
     */
    public function isValid($amount = null, $userId = null)
    {
        // Check if active
        if (!$this->is_active) {
            return false;
        }

        // Check date range
        $now = now();
        if ($now < $this->starts_at || $now > $this->expires_at) {
            return false;
        }

        // Check usage limit
        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return false;
        }

        // Check minimum amount
        if ($this->minimum_amount && $amount < $this->minimum_amount) {
            return false;
        }

        // Check per-user usage limit
        if ($this->usage_limit_per_user && $userId) {
            // This would require a coupon_usage table to track per-user usage
            // For now, we'll skip this check
        }

        return true;
    }

    /**
     * Calculate discount amount
     */
    public function calculateDiscount($amount)
    {
        if (!$this->isValid($amount)) {
            return 0;
        }

        if ($this->type === 'fixed') {
            $discount = $this->value;
        } else {
            $discount = ($amount * $this->value) / 100;
        }

        // Apply maximum discount limit
        if ($this->maximum_discount) {
            $discount = min($discount, $this->maximum_discount);
        }

        return min($discount, $amount);
    }

    /**
     * Scope for active coupons
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('starts_at', '<=', now())
                    ->where('expires_at', '>=', now());
    }
}
