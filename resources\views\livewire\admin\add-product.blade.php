<div>
    <!-- Add Product Button -->
    <flux:button wire:click="openModal" variant="primary" class="mb-6">
        <flux:icon name="plus" class="h-5 w-5 mr-2" />
        Tambah Produk Baru
    </flux:button>

    <!-- Modal -->
    <div x-data="{ open: @entangle('isOpen') }" x-show="open" class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div x-show="open" 
                 x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" 
                 @click="$wire.closeModal()"></div>

            <!-- Modal panel -->
            <div x-show="open"
                 x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-2xl">
                
                <!-- Header -->
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white">
                        Tambah Produk Baru
                    </h3>
                    <button wire:click="closeModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <flux:icon name="x-mark" class="h-6 w-6" />
                    </button>
                </div>

                <!-- Form -->
                <form wire:submit="save" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Product Name -->
                        <div class="md:col-span-2">
                            <flux:input 
                                wire:model="name"
                                label="Nama Produk"
                                placeholder="Masukkan nama produk"
                                required
                            />
                        </div>

                        <!-- Description -->
                        <div class="md:col-span-2">
                            <flux:textarea 
                                wire:model="description"
                                label="Deskripsi"
                                placeholder="Masukkan deskripsi produk"
                                rows="4"
                                required
                            />
                        </div>

                        <!-- Price -->
                        <div>
                            <flux:input 
                                wire:model="price"
                                label="Harga (Rp)"
                                type="number"
                                step="0.01"
                                min="0"
                                placeholder="0"
                                required
                            />
                        </div>

                        <!-- Sale Price -->
                        <div>
                            <flux:input 
                                wire:model="sale_price"
                                label="Harga Diskon (Rp)"
                                type="number"
                                step="0.01"
                                min="0"
                                placeholder="Opsional"
                            />
                        </div>

                        <!-- Category -->
                        <div>
                            <flux:select wire:model="category_id" label="Kategori" required>
                                <option value="">Pilih Kategori</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </flux:select>
                        </div>

                        <!-- Brand -->
                        <div>
                            <flux:input 
                                wire:model="brand"
                                label="Merek"
                                placeholder="Masukkan merek produk"
                            />
                        </div>

                        <!-- SKU -->
                        <div>
                            <flux:input 
                                wire:model="sku"
                                label="SKU"
                                placeholder="Auto-generate jika kosong"
                            />
                        </div>

                        <!-- Weight -->
                        <div>
                            <flux:input 
                                wire:model="weight"
                                label="Berat (kg)"
                                type="number"
                                step="0.01"
                                min="0"
                                placeholder="0"
                            />
                        </div>

                        <!-- Stock Quantity -->
                        <div>
                            <flux:input 
                                wire:model="stock_quantity"
                                label="Jumlah Stok"
                                type="number"
                                min="0"
                                placeholder="0"
                            />
                        </div>

                        <!-- Checkboxes -->
                        <div class="md:col-span-2 space-y-3">
                            <flux:checkbox wire:model="manage_stock" label="Kelola Stok" />
                            <flux:checkbox wire:model="is_featured" label="Produk Unggulan" />
                            <flux:checkbox wire:model="is_active" label="Aktif" />
                        </div>

                        <!-- Images -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Gambar Produk (Maksimal 5)
                            </label>
                            <input 
                                type="file" 
                                wire:model="images" 
                                multiple 
                                accept="image/*"
                                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                            />
                            
                            @if($images)
                                <div class="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4">
                                    @foreach($images as $index => $image)
                                        <div class="relative">
                                            <img src="{{ $image->temporaryUrl() }}" class="w-full h-24 object-cover rounded-lg">
                                            <button 
                                                type="button"
                                                wire:click="removeImage({{ $index }})"
                                                class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                                            >
                                                <flux:icon name="x-mark" class="h-3 w-3" />
                                            </button>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <flux:button wire:click="closeModal" variant="ghost">
                            Batal
                        </flux:button>
                        <flux:button type="submit" variant="primary" :disabled="$isLoading">
                            <div wire:loading.remove wire:target="save">
                                Simpan Produk
                            </div>
                            <div wire:loading wire:target="save" class="flex items-center gap-2">
                                <div class="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                                Menyimpan...
                            </div>
                        </flux:button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
