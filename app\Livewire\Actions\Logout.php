<?php

namespace App\Livewire\Actions;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Livewire\Component;

class Logout extends Component
{
    public $class = '';

    public function mount($class = '')
    {
        $this->class = $class;
    }

    /**
     * Log the current user out of the application.
     */
    public function logout()
    {
        Auth::guard('web')->logout();

        Session::invalidate();
        Session::regenerateToken();

        return redirect('/');
    }

    /**
     * For backward compatibility with action usage
     */
    public function __invoke()
    {
        return $this->logout();
    }

    /**
     * Get the button CSS classes
     */
    public function getButtonClass()
    {
        return $this->class ?: 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700';
    }

    public function render()
    {
        return view('livewire.actions.logout');
    }
}
