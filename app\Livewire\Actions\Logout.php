<?php

namespace App\Livewire\Actions;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Livewire\Component;

class Logout extends Component
{
    /**
     * Log the current user out of the application.
     */
    public function logout()
    {
        Auth::guard('web')->logout();

        Session::invalidate();
        Session::regenerateToken();

        return redirect('/');
    }

    /**
     * For backward compatibility with action usage
     */
    public function __invoke()
    {
        return $this->logout();
    }

    public function render()
    {
        return view('livewire.actions.logout');
    }
}
