<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ isset($title) ? $title . ' - ' : '' }}Admin - {{ config('app.name', 'SportZone') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @fluxStyles
</head>
<body class="font-sans antialiased h-full bg-gray-50 dark:bg-gray-900">
    <div class="flex h-full">
        <!-- Sidebar -->
        <div class="w-64 bg-gray-800 text-white flex-shrink-0">
            <div class="p-4">
                <h1 class="text-xl font-bold">SportZone Admin</h1>
            </div>
            <nav class="mt-8">
                <div class="px-4 space-y-2">
                    <a href="{{ route('admin.dashboard') }}" wire:navigate 
                       class="flex items-center px-4 py-2 text-sm font-medium rounded-md hover:bg-gray-700 {{ request()->routeIs('admin.dashboard') ? 'bg-gray-700' : '' }}">
                        <flux:icon name="home" class="mr-3 h-5 w-5" />
                        Dashboard
                    </a>
                    <a href="#" 
                       class="flex items-center px-4 py-2 text-sm font-medium rounded-md hover:bg-gray-700">
                        <flux:icon name="cube" class="mr-3 h-5 w-5" />
                        Products
                    </a>
                    <a href="#" 
                       class="flex items-center px-4 py-2 text-sm font-medium rounded-md hover:bg-gray-700">
                        <flux:icon name="shopping-bag" class="mr-3 h-5 w-5" />
                        Orders
                    </a>
                    <a href="#" 
                       class="flex items-center px-4 py-2 text-sm font-medium rounded-md hover:bg-gray-700">
                        <flux:icon name="users" class="mr-3 h-5 w-5" />
                        Customers
                    </a>
                    <a href="#" 
                       class="flex items-center px-4 py-2 text-sm font-medium rounded-md hover:bg-gray-700">
                        <flux:icon name="tag" class="mr-3 h-5 w-5" />
                        Categories
                    </a>
                    <a href="#" 
                       class="flex items-center px-4 py-2 text-sm font-medium rounded-md hover:bg-gray-700">
                        <flux:icon name="chart-bar" class="mr-3 h-5 w-5" />
                        Analytics
                    </a>
                    <a href="#" 
                       class="flex items-center px-4 py-2 text-sm font-medium rounded-md hover:bg-gray-700">
                        <flux:icon name="cog-6-tooth" class="mr-3 h-5 w-5" />
                        Settings
                    </a>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                            {{ $title ?? 'Admin Panel' }}
                        </h2>
                        <div class="flex items-center space-x-4">
                            <a href="{{ route('products.index') }}" wire:navigate 
                               class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                                View Store
                            </a>
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <div class="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            {{ substr(auth()->user()->name, 0, 1) }}
                                        </span>
                                    </div>
                                </button>

                                <div x-show="open" @click.away="open = false" 
                                     class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50">
                                    <a href="{{ route('settings.profile') }}" wire:navigate 
                                       class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                        Profile
                                    </a>
                                    <div class="border-t border-gray-100 dark:border-gray-700"></div>
                                    <livewire:actions.logout class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Flash Messages -->
            @if (session()->has('success'))
                <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mx-6 mt-4" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            @if (session()->has('error'))
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mx-6 mt-4" role="alert">
                    <span class="block sm:inline">{{ session('error') }}</span>
                </div>
            @endif

            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                {{ $slot }}
            </main>
        </div>
    </div>

    @fluxScripts
    <script src="//unpkg.com/alpinejs" defer></script>
</body>
</html>
