<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Shopping Cart</h1>

    @if($cartItems->count() > 0)
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Cart Items -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                                Cart Items ({{ $this->itemCount }})
                            </h2>
                            <flux:button wire:click="clearCart" variant="outline" size="sm" 
                                        wire:confirm="Are you sure you want to clear your cart?">
                                Clear Cart
                            </flux:button>
                        </div>
                    </div>

                    <div class="divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($cartItems as $item)
                            <div class="p-6">
                                <div class="flex items-start space-x-4">
                                    <!-- Product Image -->
                                    <div class="flex-shrink-0 w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                                        @if($item->product->images && count($item->product->images) > 0)
                                            <img src="{{ $item->product->images[0] }}" alt="{{ $item->product->name }}" 
                                                 class="w-full h-full object-cover">
                                        @else
                                            <div class="w-full h-full flex items-center justify-center text-gray-400">
                                                <flux:icon name="photo" class="w-8 h-8" />
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Product Details -->
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                                            <a href="{{ route('products.show', $item->product) }}" wire:navigate 
                                               class="hover:text-blue-600">
                                                {{ $item->product->name }}
                                            </a>
                                        </h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $item->product->brand }}</p>
                                        
                                        @if($item->productVariation)
                                            <div class="mt-1">
                                                @foreach($item->productVariation->attributes as $key => $value)
                                                    <span class="inline-block bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xs px-2 py-1 rounded mr-1">
                                                        {{ ucfirst($key) }}: {{ $value }}
                                                    </span>
                                                @endforeach
                                            </div>
                                        @endif

                                        <div class="mt-2 flex items-center space-x-4">
                                            <!-- Quantity -->
                                            <div class="flex items-center space-x-2">
                                                <label class="text-sm text-gray-700 dark:text-gray-300">Qty:</label>
                                                <flux:input 
                                                    wire:change="updateQuantity({{ $item->id }}, $event.target.value)"
                                                    type="number" 
                                                    min="1" 
                                                    value="{{ $item->quantity }}"
                                                    class="w-16"
                                                />
                                            </div>

                                            <!-- Remove Button -->
                                            <flux:button 
                                                wire:click="removeItem({{ $item->id }})" 
                                                variant="outline" 
                                                size="sm"
                                                wire:confirm="Remove this item from cart?">
                                                <flux:icon name="trash" class="w-4 h-4" />
                                            </flux:button>
                                        </div>
                                    </div>

                                    <!-- Price -->
                                    <div class="text-right">
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">
                                            Rp {{ number_format($item->price * $item->quantity, 0, ',', '.') }}
                                        </p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">
                                            Rp {{ number_format($item->price, 0, ',', '.') }} each
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 sticky top-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Order Summary</h2>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">Subtotal</span>
                            <span class="text-gray-900 dark:text-white">
                                Rp {{ number_format($this->subtotal, 0, ',', '.') }}
                            </span>
                        </div>
                        
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">Tax (11%)</span>
                            <span class="text-gray-900 dark:text-white">
                                Rp {{ number_format($this->taxAmount, 0, ',', '.') }}
                            </span>
                        </div>
                        
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                            <div class="flex justify-between text-base font-medium">
                                <span class="text-gray-900 dark:text-white">Total</span>
                                <span class="text-gray-900 dark:text-white">
                                    Rp {{ number_format($this->total, 0, ',', '.') }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <flux:button wire:click="proceedToCheckout" variant="primary" class="w-full">
                            Proceed to Checkout
                        </flux:button>
                    </div>

                    <div class="mt-4 text-center">
                        <a href="{{ route('products.index') }}" wire:navigate 
                           class="text-sm text-blue-600 hover:text-blue-500">
                            Continue Shopping
                        </a>
                    </div>
                </div>
            </div>
        </div>
    @else
        <!-- Empty Cart -->
        <div class="text-center py-12">
            <flux:icon name="shopping-cart" class="w-24 h-24 text-gray-400 mx-auto mb-4" />
            <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-2">Your cart is empty</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-6">Start shopping to add items to your cart.</p>
            <flux:button href="{{ route('products.index') }}" wire:navigate variant="primary">
                Start Shopping
            </flux:button>
        </div>
    @endif
</div>
