<?php

use App\Livewire\Account\AddressBook;
use App\Livewire\Admin\Dashboard as AdminDashboard;
use App\Livewire\Cart\ShoppingCart;
use App\Livewire\Checkout\CheckoutProcess;
use App\Livewire\Dashboard;
use App\Livewire\Orders\OrderDetail;
use App\Livewire\Orders\OrderHistory;
use App\Livewire\Products\ProductCatalog;
use App\Livewire\Products\ProductDetail;
use App\Livewire\Wishlist\UserWishlist;
use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

// Home page - redirect to products
Route::redirect('/', '/products')->name('home');

// Public routes
Route::get('/products', ProductCatalog::class)->name('products.index');
Route::get('/products/{product:slug}', ProductDetail::class)->name('products.show');

// Cart routes (accessible to guests)
Route::get('/cart', ShoppingCart::class)->name('cart');

// Authentication required routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', Dashboard::class)->name('dashboard')->middleware('redirect.after.login');

    // Wishlist
    Route::get('/wishlist', UserWishlist::class)->name('wishlist');

    // Checkout
    Route::get('/checkout', CheckoutProcess::class)->name('checkout');

    // Orders
    Route::get('/orders', OrderHistory::class)->name('orders.index');
    Route::get('/orders/{order}', OrderDetail::class)->name('orders.show');

    // Account Management
    Route::get('/account/addresses', AddressBook::class)->name('account.addresses');

    // Settings
    Route::redirect('settings', 'settings/profile');
    Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
    Volt::route('settings/password', 'settings.password')->name('settings.password');
    Volt::route('settings/appearance', 'settings.appearance')->name('settings.appearance');
});

// Admin routes
Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', AdminDashboard::class)->name('dashboard');
});

require __DIR__.'/auth.php';
