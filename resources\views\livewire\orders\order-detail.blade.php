<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Order #{{ $order->order_number }}</h1>
            <p class="text-gray-600 dark:text-gray-400">Placed on {{ $order->created_at->format('M d, Y \a\t g:i A') }}</p>
        </div>
        <div class="flex items-center space-x-4">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $this->statusColor }}">
                {{ ucfirst($order->status) }}
            </span>
            @if($order->canBeCancelled())
                <flux:button wire:click="cancelOrder" variant="outline" 
                            wire:confirm="Are you sure you want to cancel this order?">
                    Cancel Order
                </flux:button>
            @endif
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Order Status Timeline -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Order Status</h2>
                
                <div class="space-y-4">
                    @php
                        $statuses = [
                            'pending' => 'Order Placed',
                            'processing' => 'Processing',
                            'shipped' => 'Shipped',
                            'delivered' => 'Delivered'
                        ];
                        $currentStatusIndex = array_search($order->status, array_keys($statuses));
                    @endphp
                    
                    @foreach($statuses as $status => $label)
                        @php
                            $statusIndex = array_search($status, array_keys($statuses));
                            $isCompleted = $statusIndex <= $currentStatusIndex && $order->status !== 'cancelled';
                            $isCurrent = $status === $order->status;
                        @endphp
                        
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 rounded-full
                                        {{ $isCompleted ? 'bg-green-500 text-white' : 
                                           ($isCurrent ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600') }}">
                                @if($isCompleted && !$isCurrent)
                                    <flux:icon name="check" class="w-4 h-4" />
                                @else
                                    {{ $statusIndex + 1 }}
                                @endif
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $label }}</p>
                                @if($status === 'shipped' && $order->shipped_at)
                                    <p class="text-xs text-gray-500">{{ $order->shipped_at->format('M d, Y g:i A') }}</p>
                                @elseif($status === 'delivered' && $order->delivered_at)
                                    <p class="text-xs text-gray-500">{{ $order->delivered_at->format('M d, Y g:i A') }}</p>
                                @endif
                            </div>
                        </div>
                    @endforeach

                    @if($order->status === 'cancelled')
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 rounded-full bg-red-500 text-white">
                                <flux:icon name="x-mark" class="w-4 h-4" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-red-600">Order Cancelled</p>
                                <p class="text-xs text-gray-500">{{ $order->updated_at->format('M d, Y g:i A') }}</p>
                            </div>
                        </div>
                    @endif
                </div>

                @if($order->tracking_number)
                    <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <h3 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">Tracking Information</h3>
                        <p class="text-sm text-blue-700 dark:text-blue-300">
                            Tracking Number: <span class="font-mono">{{ $order->tracking_number }}</span>
                        </p>
                        <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                            Shipping Method: {{ $order->shipping_method }}
                        </p>
                    </div>
                @endif
            </div>

            <!-- Order Items -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Order Items</h2>
                
                <div class="space-y-6">
                    @foreach($order->items as $item)
                        <div class="flex items-start space-x-4 pb-6 border-b border-gray-200 dark:border-gray-700 last:border-b-0 last:pb-0">
                            <div class="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                                @if($item->product && $item->product->images && count($item->product->images) > 0)
                                    <img src="{{ $item->product->images[0] }}" alt="{{ $item->product_name }}" 
                                         class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full flex items-center justify-center text-gray-400">
                                        <flux:icon name="photo" class="w-8 h-8" />
                                    </div>
                                @endif
                            </div>
                            
                            <div class="flex-1">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $item->product_name }}</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">SKU: {{ $item->product_sku }}</p>
                                
                                @if($item->product_attributes)
                                    <div class="mt-2">
                                        @foreach($item->product_attributes as $key => $value)
                                            <span class="inline-block bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xs px-2 py-1 rounded mr-1">
                                                {{ ucfirst($key) }}: {{ $value }}
                                            </span>
                                        @endforeach
                                    </div>
                                @endif
                                
                                <div class="mt-2 flex items-center justify-between">
                                    <div>
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Quantity: {{ $item->quantity }}</span>
                                        <span class="text-sm text-gray-600 dark:text-gray-400 ml-4">
                                            Unit Price: Rp {{ number_format($item->unit_price, 0, ',', '.') }}
                                        </span>
                                    </div>
                                    <div class="text-lg font-semibold text-gray-900 dark:text-white">
                                        Rp {{ number_format($item->total_price, 0, ',', '.') }}
                                    </div>
                                </div>

                                @if($order->status === 'delivered' && $item->product)
                                    <div class="mt-3">
                                        <flux:button href="{{ route('products.show', $item->product) }}" wire:navigate 
                                                    variant="outline" size="sm">
                                            Buy Again
                                        </flux:button>
                                        <flux:button href="{{ route('reviews.create', ['product' => $item->product, 'order' => $order]) }}" 
                                                    wire:navigate variant="primary" size="sm" class="ml-2">
                                            Write Review
                                        </flux:button>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Shipping Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Shipping Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Shipping Address</h3>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            <p>{{ $order->shipping_address['first_name'] }} {{ $order->shipping_address['last_name'] }}</p>
                            @if($order->shipping_address['company'])
                                <p>{{ $order->shipping_address['company'] }}</p>
                            @endif
                            <p>{{ $order->shipping_address['address_line_1'] }}</p>
                            @if($order->shipping_address['address_line_2'])
                                <p>{{ $order->shipping_address['address_line_2'] }}</p>
                            @endif
                            <p>{{ $order->shipping_address['city'] }}, {{ $order->shipping_address['state'] }} {{ $order->shipping_address['postal_code'] }}</p>
                            <p>{{ $order->shipping_address['country'] }}</p>
                            <p>{{ $order->shipping_address['phone'] }}</p>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Billing Address</h3>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            <p>{{ $order->billing_address['first_name'] }} {{ $order->billing_address['last_name'] }}</p>
                            @if($order->billing_address['company'])
                                <p>{{ $order->billing_address['company'] }}</p>
                            @endif
                            <p>{{ $order->billing_address['address_line_1'] }}</p>
                            @if($order->billing_address['address_line_2'])
                                <p>{{ $order->billing_address['address_line_2'] }}</p>
                            @endif
                            <p>{{ $order->billing_address['city'] }}, {{ $order->billing_address['state'] }} {{ $order->billing_address['postal_code'] }}</p>
                            <p>{{ $order->billing_address['country'] }}</p>
                            <p>{{ $order->billing_address['phone'] }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Summary Sidebar -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 sticky top-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Order Summary</h2>
                
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Subtotal</span>
                        <span class="text-gray-900 dark:text-white">Rp {{ number_format($order->subtotal, 0, ',', '.') }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Shipping</span>
                        <span class="text-gray-900 dark:text-white">
                            @if($order->shipping_amount > 0)
                                Rp {{ number_format($order->shipping_amount, 0, ',', '.') }}
                            @else
                                Free
                            @endif
                        </span>
                    </div>
                    
                    @if($order->discount_amount > 0)
                        <div class="flex justify-between text-green-600">
                            <span>Discount</span>
                            <span>-Rp {{ number_format($order->discount_amount, 0, ',', '.') }}</span>
                        </div>
                    @endif
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Tax</span>
                        <span class="text-gray-900 dark:text-white">Rp {{ number_format($order->tax_amount, 0, ',', '.') }}</span>
                    </div>
                    
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                        <div class="flex justify-between text-base font-medium">
                            <span class="text-gray-900 dark:text-white">Total</span>
                            <span class="text-gray-900 dark:text-white">Rp {{ number_format($order->total_amount, 0, ',', '.') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Payment Information</h3>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        <p>Method: {{ ucfirst(str_replace('_', ' ', $order->payment_method)) }}</p>
                        <p>Status: 
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium {{ $this->paymentStatusColor }}">
                                {{ ucfirst($order->payment_status) }}
                            </span>
                        </p>
                        @if($order->payment_date)
                            <p>Paid on: {{ $order->payment_date->format('M d, Y g:i A') }}</p>
                        @endif
                    </div>
                </div>

                @if($order->notes)
                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Order Notes</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $order->notes }}</p>
                    </div>
                @endif

                <div class="mt-6">
                    <flux:button href="{{ route('orders.index') }}" wire:navigate variant="outline" class="w-full">
                        Back to Orders
                    </flux:button>
                </div>
            </div>
        </div>
    </div>
</div>
