<?php

namespace App\Livewire\Components;

use App\Models\Cart;
use App\Models\Product;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;
use Livewire\Component;

class AddToCart extends Component
{
    public Product $product;
    public $quantity = 1;
    public $selectedVariation = null;
    public $isLoading = false;

    public function mount(Product $product)
    {
        $this->product = $product;
        
        // Set default variation if available
        if ($product->variations->count() > 0) {
            $this->selectedVariation = $product->variations->first()->id;
        }
    }

    public function addToCart()
    {
        $this->isLoading = true;

        try {
            // Validate stock
            if ($this->product->manage_stock && $this->product->stock_quantity < $this->quantity) {
                $this->dispatch('notify', 'error', 'Stok tidak mencukupi!');
                return;
            }

            // Get user ID or session ID
            $userId = Auth::id();
            $sessionId = $userId ? null : session()->getId();

            // Check if item already exists in cart
            $existingCart = Cart::where('product_id', $this->product->id)
                ->where('product_variation_id', $this->selectedVariation)
                ->when($userId, fn($q) => $q->where('user_id', $userId))
                ->when(!$userId, fn($q) => $q->where('session_id', $sessionId))
                ->first();

            if ($existingCart) {
                // Update quantity
                $newQuantity = $existingCart->quantity + $this->quantity;
                
                // Check stock again for new total
                if ($this->product->manage_stock && $this->product->stock_quantity < $newQuantity) {
                    $this->dispatch('notify', 'error', 'Stok tidak mencukupi untuk jumlah tersebut!');
                    return;
                }
                
                $existingCart->update(['quantity' => $newQuantity]);
            } else {
                // Create new cart item
                Cart::create([
                    'user_id' => $userId,
                    'session_id' => $sessionId,
                    'product_id' => $this->product->id,
                    'product_variation_id' => $this->selectedVariation,
                    'quantity' => $this->quantity,
                    'price' => $this->product->sale_price ?? $this->product->price,
                ]);
            }

            // Update cart counter
            $this->dispatch('cart-updated');
            
            // Show success notification
            $this->dispatch('notify', 'success', 'Produk berhasil ditambahkan ke keranjang!');
            
        } catch (\Exception $e) {
            $this->dispatch('notify', 'error', 'Terjadi kesalahan saat menambahkan produk ke keranjang.');
        } finally {
            $this->isLoading = false;
        }
    }

    public function render()
    {
        return view('livewire.components.add-to-cart');
    }
}
