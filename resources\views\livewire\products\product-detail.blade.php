<div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        <!-- Product Images -->
        <div class="space-y-4">
            <div class="aspect-square bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                @if($product->images && count($product->images) > 0)
                    <img src="{{ $product->images[0] }}" alt="{{ $product->name }}" 
                         class="w-full h-full object-cover">
                @else
                    <div class="w-full h-full flex items-center justify-center text-gray-400">
                        <flux:icon name="photo" class="w-24 h-24" />
                    </div>
                @endif
            </div>
            
            @if($product->images && count($product->images) > 1)
                <div class="grid grid-cols-4 gap-2">
                    @foreach(array_slice($product->images, 1, 4) as $image)
                        <div class="aspect-square bg-gray-100 dark:bg-gray-700 rounded overflow-hidden">
                            <img src="{{ $image }}" alt="{{ $product->name }}" 
                                 class="w-full h-full object-cover cursor-pointer hover:opacity-80">
                        </div>
                    @endforeach
                </div>
            @endif
        </div>

        <!-- Product Info -->
        <div class="space-y-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">{{ $product->name }}</h1>
                <p class="text-lg text-gray-600 dark:text-gray-400">{{ $product->brand }}</p>
                
                <div class="flex items-center mt-2">
                    @for($i = 1; $i <= 5; $i++)
                        <flux:icon name="star" class="w-5 h-5 {{ $i <= $product->rating ? 'text-yellow-400' : 'text-gray-300' }}" />
                    @endfor
                    <span class="text-gray-600 dark:text-gray-400 ml-2">({{ $product->review_count }} reviews)</span>
                </div>
            </div>

            <!-- Price -->
            <div class="border-t border-b border-gray-200 dark:border-gray-700 py-4">
                @if($product->is_on_sale)
                    <div class="flex items-center space-x-2">
                        <span class="text-3xl font-bold text-gray-900 dark:text-white">
                            Rp {{ number_format($this->currentPrice, 0, ',', '.') }}
                        </span>
                        <span class="text-xl text-gray-500 line-through">
                            Rp {{ number_format($product->price, 0, ',', '.') }}
                        </span>
                        <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm font-medium">
                            Save {{ $product->discount_percentage }}%
                        </span>
                    </div>
                @else
                    <span class="text-3xl font-bold text-gray-900 dark:text-white">
                        Rp {{ number_format($this->currentPrice, 0, ',', '.') }}
                    </span>
                @endif
            </div>

            <!-- Product Variations -->
            @if($product->variations->count() > 0)
                <div class="space-y-4">
                    @php
                        $attributeTypes = $product->variations->pluck('attributes')->flatten(1)->keys()->unique();
                    @endphp
                    
                    @foreach($attributeTypes as $attributeType)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ ucfirst($attributeType) }}
                            </label>
                            <div class="flex flex-wrap gap-2">
                                @php
                                    $values = $product->variations->pluck('attributes.' . $attributeType)->unique()->filter();
                                @endphp
                                @foreach($values as $value)
                                    <button 
                                        wire:click="$set('selectedAttributes.{{ $attributeType }}', '{{ $value }}')"
                                        class="px-3 py-2 border rounded-md text-sm font-medium transition-colors
                                               {{ ($selectedAttributes[$attributeType] ?? '') === $value 
                                                  ? 'border-blue-500 bg-blue-50 text-blue-700' 
                                                  : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50' }}">
                                        {{ $value }}
                                    </button>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif

            <!-- Stock Status -->
            <div class="flex items-center space-x-2">
                @if($this->currentStock > 0)
                    <flux:icon name="check-circle" class="w-5 h-5 text-green-500" />
                    <span class="text-green-600 font-medium">In Stock ({{ $this->currentStock }} available)</span>
                @else
                    <flux:icon name="x-circle" class="w-5 h-5 text-red-500" />
                    <span class="text-red-600 font-medium">Out of Stock</span>
                @endif
            </div>

            <!-- Quantity and Add to Cart -->
            @if($this->currentStock > 0)
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quantity</label>
                        <flux:input 
                            wire:model="quantity" 
                            type="number" 
                            min="1" 
                            max="{{ $this->currentStock }}"
                            class="w-20"
                        />
                    </div>

                    <div class="flex space-x-4">
                        <flux:button wire:click="addToCart" variant="primary" class="flex-1">
                            <flux:icon name="shopping-cart" class="w-5 h-5 mr-2" />
                            Add to Cart
                        </flux:button>
                        
                        @if($this->isInWishlist)
                            <flux:button wire:click="removeFromWishlist" variant="outline">
                                <flux:icon name="heart" class="w-5 h-5 text-red-500" />
                            </flux:button>
                        @else
                            <flux:button wire:click="addToWishlist" variant="outline">
                                <flux:icon name="heart" class="w-5 h-5" />
                            </flux:button>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Product Description -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Description</h3>
                <div class="prose prose-sm max-w-none text-gray-600 dark:text-gray-400">
                    {!! nl2br(e($product->description)) !!}
                </div>
            </div>

            <!-- Product Specifications -->
            @if($product->attributes)
                <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Specifications</h3>
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
                        @foreach($product->attributes as $key => $value)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ ucfirst($key) }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $value }}</dd>
                            </div>
                        @endforeach
                    </dl>
                </div>
            @endif
        </div>
    </div>

    <!-- Reviews Section -->
    <div class="border-t border-gray-200 dark:border-gray-700 pt-12">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Customer Reviews</h2>
        
        @if($product->reviews->count() > 0)
            <div class="space-y-6">
                @foreach($product->reviews->take(5) as $review)
                    <div class="border-b border-gray-200 dark:border-gray-700 pb-6">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium text-gray-700">
                                        {{ substr($review->user->name, 0, 1) }}
                                    </span>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-1">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ $review->user->name }}</h4>
                                    <div class="flex">
                                        @for($i = 1; $i <= 5; $i++)
                                            <flux:icon name="star" class="w-4 h-4 {{ $i <= $review->rating ? 'text-yellow-400' : 'text-gray-300' }}" />
                                        @endfor
                                    </div>
                                    @if($review->is_verified_purchase)
                                        <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Verified Purchase</span>
                                    @endif
                                </div>
                                @if($review->title)
                                    <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-1">{{ $review->title }}</h5>
                                @endif
                                @if($review->comment)
                                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ $review->comment }}</p>
                                @endif
                                <p class="text-xs text-gray-500 mt-2">{{ $review->created_at->format('M d, Y') }}</p>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <p class="text-gray-600 dark:text-gray-400">No reviews yet. Be the first to review this product!</p>
        @endif
    </div>
</div>
