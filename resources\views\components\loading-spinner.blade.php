@props(['size' => 'md', 'color' => 'blue'])

@php
$sizeClasses = [
    'sm' => 'h-4 w-4',
    'md' => 'h-8 w-8',
    'lg' => 'h-12 w-12',
    'xl' => 'h-16 w-16'
];

$colorClasses = [
    'blue' => 'text-blue-600',
    'white' => 'text-white',
    'gray' => 'text-gray-600'
];
@endphp

<div {{ $attributes->merge(['class' => 'flex items-center justify-center']) }}>
    <div class="animate-spin rounded-full border-2 border-gray-300 border-t-current {{ $sizeClasses[$size] }} {{ $colorClasses[$color] }}"></div>
</div>
