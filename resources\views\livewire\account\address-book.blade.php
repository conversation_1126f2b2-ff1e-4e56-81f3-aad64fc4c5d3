<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Address Book</h1>
        <flux:button wire:click="addAddress" variant="primary">
            <flux:icon name="plus" class="w-4 h-4 mr-2" />
            Add New Address
        </flux:button>
    </div>

    <!-- Addresses Grid -->
    @if($addresses->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($addresses as $address)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border {{ $address->is_default ? 'border-blue-500' : 'border-gray-200 dark:border-gray-700' }} p-6">
                    <!-- Address Type & Default Badge -->
                    <div class="flex items-center justify-between mb-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                   {{ $address->type === 'shipping' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                            {{ ucfirst($address->type) }}
                        </span>
                        @if($address->is_default)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Default
                            </span>
                        @endif
                    </div>

                    <!-- Address Details -->
                    <div class="space-y-2 mb-4">
                        <h3 class="font-semibold text-gray-900 dark:text-white">{{ $address->full_name }}</h3>
                        @if($address->company)
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $address->company }}</p>
                        @endif
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $address->formatted_address }}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $address->phone }}</p>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div class="flex space-x-2">
                            <flux:button wire:click="editAddress({{ $address->id }})" variant="outline" size="sm">
                                <flux:icon name="pencil" class="w-4 h-4" />
                            </flux:button>
                            <flux:button wire:click="deleteAddress({{ $address->id }})" variant="outline" size="sm"
                                        wire:confirm="Are you sure you want to delete this address?">
                                <flux:icon name="trash" class="w-4 h-4" />
                            </flux:button>
                        </div>
                        
                        @if(!$address->is_default)
                            <flux:button wire:click="setAsDefault({{ $address->id }})" variant="outline" size="sm">
                                Set Default
                            </flux:button>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <flux:icon name="map-pin" class="w-24 h-24 text-gray-400 mx-auto mb-4" />
            <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-2">No addresses found</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-6">Add your first address to get started with faster checkout.</p>
            <flux:button wire:click="addAddress" variant="primary">
                Add Your First Address
            </flux:button>
        </div>
    @endif

    <!-- Address Form Modal -->
    @if($showForm)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                            {{ $editingAddress ? 'Edit Address' : 'Add New Address' }}
                        </h2>
                        <flux:button wire:click="closeForm" variant="ghost" size="sm">
                            <flux:icon name="x-mark" class="w-5 h-5" />
                        </flux:button>
                    </div>

                    <form wire:submit="saveAddress" class="space-y-6">
                        <!-- Address Type -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address Type</label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" wire:model="form.type" value="shipping" class="mr-2">
                                    <span class="text-sm">Shipping Address</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" wire:model="form.type" value="billing" class="mr-2">
                                    <span class="text-sm">Billing Address</span>
                                </label>
                            </div>
                        </div>

                        <!-- Name Fields -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <flux:input wire:model="form.first_name" label="First Name" required />
                            <flux:input wire:model="form.last_name" label="Last Name" required />
                        </div>

                        <!-- Company -->
                        <flux:input wire:model="form.company" label="Company (Optional)" />

                        <!-- Address Lines -->
                        <flux:input wire:model="form.address_line_1" label="Address Line 1" required />
                        <flux:input wire:model="form.address_line_2" label="Address Line 2 (Optional)" />

                        <!-- City, State, Postal Code -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <flux:input wire:model="form.city" label="City" required />
                            <flux:input wire:model="form.state" label="State/Province" required />
                            <flux:input wire:model="form.postal_code" label="Postal Code" required />
                        </div>

                        <!-- Country and Phone -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <flux:input wire:model="form.country" label="Country" required />
                            <flux:input wire:model="form.phone" label="Phone Number" required />
                        </div>

                        <!-- Default Address -->
                        <flux:checkbox wire:model="form.is_default" label="Set as default address" />

                        <!-- Form Actions -->
                        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                            <flux:button wire:click="closeForm" variant="outline" type="button">
                                Cancel
                            </flux:button>
                            <flux:button type="submit" variant="primary">
                                {{ $editingAddress ? 'Update Address' : 'Save Address' }}
                            </flux:button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
</div>
