<?php

namespace App\Livewire\Admin;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\WithFileUploads;

class AddProduct extends Component
{
    use WithFileUploads;

    #[Validate('required|string|max:255')]
    public $name = '';

    #[Validate('required|string')]
    public $description = '';

    #[Validate('required|numeric|min:0')]
    public $price = '';

    #[Validate('nullable|numeric|min:0')]
    public $sale_price = '';

    #[Validate('required|exists:categories,id')]
    public $category_id = '';

    #[Validate('nullable|string|max:255')]
    public $brand = '';

    #[Validate('nullable|string|max:10')]
    public $sku = '';

    #[Validate('nullable|numeric|min:0')]
    public $weight = '';

    #[Validate('nullable|integer|min:0')]
    public $stock_quantity = 0;

    #[Validate('boolean')]
    public $manage_stock = true;

    #[Validate('boolean')]
    public $is_featured = false;

    #[Validate('boolean')]
    public $is_active = true;

    #[Validate('nullable|array|max:5')]
    public $images = [];

    public $isOpen = false;
    public $isLoading = false;

    public function mount()
    {
        // Set default category if available
        $firstCategory = Category::first();
        if ($firstCategory) {
            $this->category_id = $firstCategory->id;
        }
    }

    public function openModal()
    {
        $this->isOpen = true;
        $this->resetForm();
    }

    public function closeModal()
    {
        $this->isOpen = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->reset([
            'name', 'description', 'price', 'sale_price', 'brand', 'sku', 
            'weight', 'stock_quantity', 'images'
        ]);
        $this->manage_stock = true;
        $this->is_featured = false;
        $this->is_active = true;
        
        $firstCategory = Category::first();
        if ($firstCategory) {
            $this->category_id = $firstCategory->id;
        }
    }

    public function save()
    {
        $this->isLoading = true;

        try {
            $this->validate();

            // Generate slug
            $slug = Str::slug($this->name);
            $originalSlug = $slug;
            $counter = 1;
            
            while (Product::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Handle image uploads
            $imageUrls = [];
            if ($this->images) {
                foreach ($this->images as $image) {
                    $filename = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
                    $path = $image->storeAs('products', $filename, 'public');
                    $imageUrls[] = Storage::url($path);
                }
            }

            // Create product
            $product = Product::create([
                'name' => $this->name,
                'slug' => $slug,
                'description' => $this->description,
                'price' => $this->price,
                'sale_price' => $this->sale_price ?: null,
                'category_id' => $this->category_id,
                'brand' => $this->brand,
                'sku' => $this->sku ?: Str::upper(Str::random(8)),
                'weight' => $this->weight,
                'stock_quantity' => $this->stock_quantity,
                'manage_stock' => $this->manage_stock,
                'is_featured' => $this->is_featured,
                'is_active' => $this->is_active,
                'images' => $imageUrls,
                'rating' => 0,
                'review_count' => 0,
            ]);

            $this->dispatch('notify', 'success', 'Produk berhasil ditambahkan!');
            $this->dispatch('product-added', $product->id);
            $this->closeModal();

        } catch (\Exception $e) {
            $this->dispatch('notify', 'error', 'Terjadi kesalahan saat menambahkan produk: ' . $e->getMessage());
        } finally {
            $this->isLoading = false;
        }
    }

    public function removeImage($index)
    {
        unset($this->images[$index]);
        $this->images = array_values($this->images);
    }

    public function render()
    {
        return view('livewire.admin.add-product', [
            'categories' => Category::orderBy('name')->get()
        ]);
    }
}
