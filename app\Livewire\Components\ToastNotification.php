<?php

namespace App\Livewire\Components;

use Livewire\Attributes\On;
use Livewire\Component;

class ToastNotification extends Component
{
    public $notifications = [];

    #[On('notify')]
    public function addNotification($type, $message, $duration = 5000)
    {
        $id = uniqid();
        $this->notifications[] = [
            'id' => $id,
            'type' => $type,
            'message' => $message,
            'duration' => $duration,
        ];

        $this->dispatch('notification-added', $id, $duration);
    }

    public function removeNotification($id)
    {
        $this->notifications = array_filter($this->notifications, function ($notification) use ($id) {
            return $notification['id'] !== $id;
        });
    }

    public function render()
    {
        return view('livewire.components.toast-notification');
    }
}
