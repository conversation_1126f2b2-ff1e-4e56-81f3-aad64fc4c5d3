<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RedirectAfterLogin
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Only redirect if user just logged in and is on dashboard route
        if (Auth::check() && $request->route()->getName() === 'dashboard') {
            $user = Auth::user();
            
            // Redirect admin to admin dashboard
            if ($user->isAdmin()) {
                return redirect()->route('admin.dashboard');
            }
            
            // Redirect regular user to products page (store)
            return redirect()->route('products.index');
        }

        return $response;
    }
}
