<div>
    <div class="flex items-center gap-3">
        <!-- Quantity Selector -->
        <div class="flex items-center border border-gray-300 dark:border-gray-600 rounded-lg">
            <button
                wire:click="$set('quantity', {{ max(1, $quantity - 1) }})"
                class="px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                {{ $quantity <= 1 ? 'disabled' : '' }}
            >
                <flux:icon name="minus" class="h-4 w-4" />
            </button>

            <input
                type="number"
                wire:model.live="quantity"
                min="1"
                max="{{ $product->stock_quantity }}"
                class="w-16 text-center border-0 bg-transparent text-gray-900 dark:text-white focus:ring-0"
            />

            <button
                wire:click="$set('quantity', {{ $quantity + 1 }})"
                class="px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                {{ $product->manage_stock && $quantity >= $product->stock_quantity ? 'disabled' : '' }}
            >
                <flux:icon name="plus" class="h-4 w-4" />
            </button>
        </div>

        <!-- Add to Cart Button -->
        <button
            wire:click="addToCart"
            wire:loading.attr="disabled"
            wire:target="addToCart"
            class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-500 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 disabled:hover:scale-100 shadow-md hover:shadow-lg disabled:shadow-none flex items-center justify-center gap-2"
            {{ $product->stock_quantity == 0 ? 'disabled' : '' }}
        >
            <div wire:loading.remove wire:target="addToCart" class="flex items-center gap-2">
                <flux:icon name="shopping-cart" class="h-5 w-5" />
                @if($product->stock_quantity == 0)
                    Stok Habis
                @else
                    Tambah ke Keranjang
                @endif
            </div>

            <div wire:loading wire:target="addToCart" class="flex items-center gap-2">
                <div class="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                Menambahkan...
            </div>
        </button>
    </div>

    @if($product->variations && $product->variations->count() > 0)
        <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Pilih Variasi:
            </label>
            <select
                wire:model.live="selectedVariation"
                class="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
                @foreach($product->variations as $variation)
                    <option value="{{ $variation->id }}">
                        {{ $variation->attributes['name'] ?? 'Variasi ' . $variation->id }}
                        @if(isset($variation->price) && $variation->price != $product->price)
                            - Rp {{ number_format($variation->price, 0, ',', '.') }}
                        @endif
                    </option>
                @endforeach
            </select>
        </div>
    @endif
</div>
