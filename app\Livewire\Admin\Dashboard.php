<?php

namespace App\Livewire\Admin;

use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Component;

class Dashboard extends Component
{
    public function mount()
    {
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }
    }

    public function getTotalUsersProperty()
    {
        return User::where('role', 'user')->count();
    }

    public function getTotalProductsProperty()
    {
        return Product::count();
    }

    public function getTotalOrdersProperty()
    {
        return Order::count();
    }

    public function getTotalRevenueProperty()
    {
        return Order::where('payment_status', 'paid')->sum('total_amount');
    }

    public function getPendingOrdersProperty()
    {
        return Order::where('status', 'pending')->count();
    }

    public function getProcessingOrdersProperty()
    {
        return Order::where('status', 'processing')->count();
    }

    public function getRecentOrdersProperty()
    {
        return Order::with(['user', 'items'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();
    }

    public function getLowStockProductsProperty()
    {
        return Product::where('stock_quantity', '<=', 10)
            ->where('manage_stock', false)
            ->orderBy('stock_quantity', 'asc')
            ->take(5)
            ->get();
    }

    #[Layout('layouts.admin')]
    #[Title('Admin Dashboard')]
    public function render()
    {
        return view('livewire.admin.dashboard');
    }
}
