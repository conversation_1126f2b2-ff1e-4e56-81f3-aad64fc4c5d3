<?php

namespace App\Livewire\Checkout;

use App\Models\Address;
use App\Models\Cart;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ShippingMethod;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Component;

class CheckoutProcess extends Component
{
    public $step = 1; // 1: Address, 2: Shipping, 3: Payment, 4: Review
    public $cartItems = [];
    public $addresses = [];
    public $shippingMethods = [];
    
    // Form data
    public $selectedBillingAddress = null;
    public $selectedShippingAddress = null;
    public $selectedShippingMethod = null;
    public $paymentMethod = 'bank_transfer';
    public $couponCode = '';
    public $appliedCoupon = null;
    public $notes = '';
    
    // Address form for new address
    public $showAddressForm = false;
    public $addressForm = [
        'type' => 'shipping',
        'first_name' => '',
        'last_name' => '',
        'company' => '',
        'address_line_1' => '',
        'address_line_2' => '',
        'city' => '',
        'state' => '',
        'postal_code' => '',
        'country' => 'Indonesia',
        'phone' => '',
        'is_default' => false,
    ];

    public function mount()
    {
        if (!Auth::check()) {
            return $this->redirect(route('login'));
        }

        $this->loadCartItems();
        $this->loadAddresses();
        $this->loadShippingMethods();

        if ($this->cartItems->isEmpty()) {
            session()->flash('error', 'Your cart is empty.');
            return $this->redirect(route('cart'));
        }
    }

    public function loadCartItems()
    {
        $this->cartItems = Cart::with(['product', 'productVariation'])
            ->where('user_id', Auth::id())
            ->get();
    }

    public function loadAddresses()
    {
        $this->addresses = Address::where('user_id', Auth::id())->get();
        
        // Set default addresses
        if (!$this->selectedBillingAddress) {
            $defaultBilling = $this->addresses->where('type', 'billing')->where('is_default', true)->first();
            $this->selectedBillingAddress = $defaultBilling?->id;
        }
        
        if (!$this->selectedShippingAddress) {
            $defaultShipping = $this->addresses->where('type', 'shipping')->where('is_default', true)->first();
            $this->selectedShippingAddress = $defaultShipping?->id;
        }
    }

    public function loadShippingMethods()
    {
        $this->shippingMethods = ShippingMethod::active()->get();
    }

    public function nextStep()
    {
        if ($this->step < 4) {
            $this->step++;
        }
    }

    public function previousStep()
    {
        if ($this->step > 1) {
            $this->step--;
        }
    }

    public function saveAddress()
    {
        $this->validate([
            'addressForm.first_name' => 'required|string|max:255',
            'addressForm.last_name' => 'required|string|max:255',
            'addressForm.address_line_1' => 'required|string|max:255',
            'addressForm.city' => 'required|string|max:255',
            'addressForm.state' => 'required|string|max:255',
            'addressForm.postal_code' => 'required|string|max:10',
            'addressForm.phone' => 'required|string|max:20',
        ]);

        $address = Address::create([
            'user_id' => Auth::id(),
            ...$this->addressForm
        ]);

        $this->loadAddresses();
        $this->showAddressForm = false;
        $this->reset('addressForm');
        
        // Auto-select the new address
        if ($address->type === 'billing') {
            $this->selectedBillingAddress = $address->id;
        } else {
            $this->selectedShippingAddress = $address->id;
        }

        session()->flash('success', 'Address saved successfully!');
    }

    public function applyCoupon()
    {
        if (!$this->couponCode) {
            return;
        }

        $coupon = Coupon::where('code', $this->couponCode)->first();
        
        if (!$coupon || !$coupon->isValid($this->subtotal, Auth::id())) {
            session()->flash('error', 'Invalid or expired coupon code.');
            return;
        }

        $this->appliedCoupon = $coupon;
        session()->flash('success', 'Coupon applied successfully!');
    }

    public function removeCoupon()
    {
        $this->appliedCoupon = null;
        $this->couponCode = '';
        session()->flash('success', 'Coupon removed.');
    }

    public function getSubtotalProperty()
    {
        return $this->cartItems->sum(function ($item) {
            return $item->price * $item->quantity;
        });
    }

    public function getShippingCostProperty()
    {
        if (!$this->selectedShippingMethod) {
            return 0;
        }

        $method = ShippingMethod::find($this->selectedShippingMethod);
        if (!$method) {
            return 0;
        }

        $totalWeight = $this->cartItems->sum(function ($item) {
            return ($item->product->weight ?? 0) * $item->quantity;
        });

        return $method->calculateCost($totalWeight, $this->subtotal);
    }

    public function getDiscountAmountProperty()
    {
        if (!$this->appliedCoupon) {
            return 0;
        }

        return $this->appliedCoupon->calculateDiscount($this->subtotal);
    }

    public function getTaxAmountProperty()
    {
        $taxableAmount = $this->subtotal + $this->shippingCost - $this->discountAmount;
        return $taxableAmount * 0.11; // 11% PPN
    }

    public function getTotalProperty()
    {
        return $this->subtotal + $this->shippingCost + $this->taxAmount - $this->discountAmount;
    }

    public function placeOrder()
    {
        // Validation
        if (!$this->selectedBillingAddress || !$this->selectedShippingAddress) {
            session()->flash('error', 'Please select billing and shipping addresses.');
            return;
        }

        if (!$this->selectedShippingMethod) {
            session()->flash('error', 'Please select a shipping method.');
            return;
        }

        try {
            DB::beginTransaction();

            // Get addresses
            $billingAddress = Address::find($this->selectedBillingAddress);
            $shippingAddress = Address::find($this->selectedShippingAddress);

            // Create order
            $order = Order::create([
                'order_number' => Order::generateOrderNumber(),
                'user_id' => Auth::id(),
                'status' => 'pending',
                'subtotal' => $this->subtotal,
                'tax_amount' => $this->taxAmount,
                'shipping_amount' => $this->shippingCost,
                'discount_amount' => $this->discountAmount,
                'total_amount' => $this->total,
                'currency' => 'IDR',
                'billing_address' => $billingAddress->toArray(),
                'shipping_address' => $shippingAddress->toArray(),
                'payment_method' => $this->paymentMethod,
                'payment_status' => 'pending',
                'shipping_method' => ShippingMethod::find($this->selectedShippingMethod)->name,
                'notes' => $this->notes,
            ]);

            // Create order items
            foreach ($this->cartItems as $cartItem) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $cartItem->product_id,
                    'product_variation_id' => $cartItem->product_variation_id,
                    'product_name' => $cartItem->product->name,
                    'product_sku' => $cartItem->productVariation?->sku ?? $cartItem->product->sku,
                    'product_attributes' => $cartItem->productVariation?->attributes,
                    'quantity' => $cartItem->quantity,
                    'unit_price' => $cartItem->price,
                    'total_price' => $cartItem->price * $cartItem->quantity,
                ]);

                // Update stock
                if ($cartItem->productVariation) {
                    $cartItem->productVariation->decrement('stock_quantity', $cartItem->quantity);
                } else {
                    $cartItem->product->decrement('stock_quantity', $cartItem->quantity);
                }
            }

            // Update coupon usage
            if ($this->appliedCoupon) {
                $this->appliedCoupon->increment('used_count');
            }

            // Clear cart
            Cart::where('user_id', Auth::id())->delete();

            DB::commit();

            session()->flash('success', 'Order placed successfully!');
            return $this->redirect(route('orders.show', $order));

        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Failed to place order. Please try again.');
        }
    }

    #[Layout('layouts.app')]
    #[Title('Checkout')]
    public function render()
    {
        return view('livewire.checkout.checkout-process');
    }
}
